"""
Context management system for ERP
Provides AsyncLocalStorage-like functionality using Python's contextvars
"""
import contextvars
from typing import Any, Dict, Optional, TypeVar, Generic, Callable, Awaitable, TYPE_CHECKING
from contextlib import asynccontextmanager
import asyncio
from functools import wraps

if TYPE_CHECKING:
    from .environment import Environment

T = TypeVar('T')


class AsyncLocalStorage(Generic[T]):
    """
    Python equivalent of Node.js AsyncLocalStorage
    Uses contextvars to maintain context across async operations
    """
    
    def __init__(self, name: str = None):
        self._context_var: contextvars.ContextVar[T] = contextvars.ContextVar(
            name or f'async_local_storage_{id(self)}'
        )
    
    def get(self, default: T = None) -> Optional[T]:
        """Get the current context value"""
        try:
            return self._context_var.get()
        except LookupError:
            return default
    
    def set(self, value: T) -> contextvars.Token:
        """Set the context value and return a token"""
        return self._context_var.set(value)
    
    def reset(self, token: contextvars.Token) -> None:
        """Reset the context to a previous state using a token"""
        self._context_var.reset(token)
    
    @asynccontextmanager
    async def run(self, value: T):
        """Run code with a specific context value"""
        token = self.set(value)
        try:
            yield value
        finally:
            self.reset(token)
    
    def run_sync(self, value: T, func: Callable[..., T], *args, **kwargs) -> T:
        """Run a synchronous function with a specific context value"""
        token = self.set(value)
        try:
            return func(*args, **kwargs)
        finally:
            self.reset(token)
    
    async def run_async(self, value: T, func: Callable[..., Awaitable[T]], *args, **kwargs) -> T:
        """Run an async function with a specific context value"""
        async with self.run(value):
            return await func(*args, **kwargs)


class ContextManager:
    """
    Global context manager for the ERP system
    Manages different types of context storage
    """
    
    # Environment context storage
    environment: AsyncLocalStorage['Environment'] = AsyncLocalStorage('erp.environment')
    
    # Request context storage
    request: AsyncLocalStorage[Dict[str, Any]] = AsyncLocalStorage('erp.request')
    
    # Database context storage
    database: AsyncLocalStorage[str] = AsyncLocalStorage('erp.database')
    
    # User context storage
    user: AsyncLocalStorage[int] = AsyncLocalStorage('erp.user')
    
    @classmethod
    def get_environment(cls) -> Optional['Environment']:
        """Get current environment from context"""
        return cls.environment.get()
    
    @classmethod
    def set_environment(cls, env: 'Environment') -> contextvars.Token:
        """Set current environment in context"""
        return cls.environment.set(env)
    
    @classmethod
    def get_request_context(cls) -> Dict[str, Any]:
        """Get current request context"""
        return cls.request.get() or {}
    
    @classmethod
    def set_request_context(cls, context: Dict[str, Any]) -> contextvars.Token:
        """Set current request context"""
        return cls.request.set(context)
    
    @classmethod
    def get_database(cls) -> Optional[str]:
        """Get current database name from context"""
        return cls.database.get()
    
    @classmethod
    def set_database(cls, db_name: str) -> contextvars.Token:
        """Set current database name in context"""
        return cls.database.set(db_name)
    
    @classmethod
    def get_user(cls) -> Optional[int]:
        """Get current user ID from context"""
        return cls.user.get()
    
    @classmethod
    def set_user(cls, user_id: int) -> contextvars.Token:
        """Set current user ID in context"""
        return cls.user.set(user_id)
    
    @classmethod
    @asynccontextmanager
    async def with_context(cls, env: 'Environment' = None, **context):
        """Context manager to run code with specific context"""
        tokens = []
        
        try:
            if env:
                tokens.append(cls.set_environment(env))
                tokens.append(cls.set_database(env.cr.db_name))
                tokens.append(cls.set_user(env.uid))
            
            if context:
                current_context = cls.get_request_context()
                new_context = {**current_context, **context}
                tokens.append(cls.set_request_context(new_context))
            
            yield
            
        finally:
            # Reset tokens in reverse order
            for token in reversed(tokens):
                if hasattr(token, 'var'):
                    token.var.reset(token)


def with_environment(func: Callable) -> Callable:
    """
    Decorator to ensure a function has access to the current environment
    """
    if asyncio.iscoroutinefunction(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            env = ContextManager.get_environment()
            if env is None:
                raise RuntimeError("No environment found in context. Use ContextManager.with_context() or set environment.")
            return await func(*args, **kwargs)
        return async_wrapper
    else:
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            env = ContextManager.get_environment()
            if env is None:
                raise RuntimeError("No environment found in context. Use ContextManager.with_context() or set environment.")
            return func(*args, **kwargs)
        return sync_wrapper


def require_database(func: Callable) -> Callable:
    """
    Decorator to ensure a function has access to a database context
    """
    if asyncio.iscoroutinefunction(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            db_name = ContextManager.get_database()
            if db_name is None:
                raise RuntimeError("No database found in context.")
            return await func(*args, **kwargs)
        return async_wrapper
    else:
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            db_name = ContextManager.get_database()
            if db_name is None:
                raise RuntimeError("No database found in context.")
            return func(*args, **kwargs)
        return sync_wrapper


def require_user(func: Callable) -> Callable:
    """
    Decorator to ensure a function has access to a user context
    """
    if asyncio.iscoroutinefunction(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            user_id = ContextManager.get_user()
            if user_id is None:
                raise RuntimeError("No user found in context.")
            return await func(*args, **kwargs)
        return async_wrapper
    else:
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            user_id = ContextManager.get_user()
            if user_id is None:
                raise RuntimeError("No user found in context.")
            return func(*args, **kwargs)
        return sync_wrapper


# Global context manager instance
context = ContextManager()
