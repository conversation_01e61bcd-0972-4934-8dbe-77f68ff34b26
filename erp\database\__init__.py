# Import from new modular structure
from .connection import DatabaseManager
from .registry import DatabaseRegistry, DatabaseLifecycle, DatabaseInitializer
from .memory import (
    DatabaseMemoryRegistry,
    MemoryRegistryManager,
    DatabaseFilterProcessor,
    get_memory_registry_manager
)

__all__ = [
    'DatabaseManager',
    'DatabaseRegistry',
    'DatabaseLifecycle',
    'DatabaseInitializer',
    'DatabaseMemoryRegistry',
    'MemoryRegistryManager',
    'DatabaseFilterProcessor',
    'get_memory_registry_manager'
]
