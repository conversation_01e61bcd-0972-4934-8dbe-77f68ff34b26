"""
Cluster management for ERP system
"""
import os
import socket
import uuid
from typing import Dict, Any, Optional
from .config import config
from .logging import get_logger


class ClusterManager:
    """Manages cluster configuration and setup"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.cluster_id = None
        self.node_id = None
        self.cluster_info = {}
        
    def is_cluster_mode(self) -> bool:
        """Check if cluster mode is enabled"""
        return config.cluster_mode
    
    def setup_cluster(self):
        """Setup cluster configuration"""
        if not self.is_cluster_mode():
            self.logger.debug("Cluster mode disabled, skipping cluster setup")
            return
            
        self.logger.info("🔗 Setting up cluster configuration...")
        
        # Get cluster nodes configuration
        cluster_nodes = self._get_cluster_nodes_config()
        self.logger.info(f"🔢 Cluster nodes configuration: {cluster_nodes}")
        
        # Generate cluster and node IDs
        self._generate_cluster_ids()
        
        # Setup cluster networking
        self._setup_cluster_networking()
        
        # Setup cluster memory monitoring
        self._setup_cluster_monitoring()
        
        # Log cluster setup completion
        self.logger.info(f"✅ Cluster setup completed - Node ID: {self.node_id}")
        self.logger.info(f"🌐 Cluster ID: {self.cluster_id}")
        
    def _get_cluster_nodes_config(self) -> str:
        """Get cluster nodes configuration"""
        cluster_nodes = config.cluster_nodes
        
        if cluster_nodes.lower() == "auto":
            # Auto-detect cluster nodes (for now, return "auto")
            # In a real implementation, this could discover other nodes
            # via network discovery, service registry, etc.
            return "auto"
        else:
            # Validate that it's a number
            try:
                nodes_count = int(cluster_nodes)
                if nodes_count <= 0:
                    self.logger.warning(f"Invalid cluster_nodes value: {cluster_nodes}, using auto")
                    return "auto"
                return str(nodes_count)
            except ValueError:
                self.logger.warning(f"Invalid cluster_nodes value: {cluster_nodes}, using auto")
                return "auto"
        
    def _generate_cluster_ids(self):
        """Generate unique cluster and node identifiers"""
        # Generate or load cluster ID
        cluster_id_file = "cluster.id"
        if os.path.exists(cluster_id_file):
            with open(cluster_id_file, 'r') as f:
                self.cluster_id = f.read().strip()
        else:
            self.cluster_id = str(uuid.uuid4())
            with open(cluster_id_file, 'w') as f:
                f.write(self.cluster_id)
        
        # Generate node ID based on hostname and process
        hostname = socket.gethostname()
        pid = os.getpid()
        self.node_id = f"{hostname}-{pid}"
        
        self.logger.debug(f"Generated cluster ID: {self.cluster_id}")
        self.logger.debug(f"Generated node ID: {self.node_id}")
    
    def _setup_cluster_networking(self):
        """Setup cluster networking configuration"""
        try:
            # Get local IP address
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            
            server_config = config.server_config
            
            self.cluster_info.update({
                'node_id': self.node_id,
                'cluster_id': self.cluster_id,
                'local_ip': local_ip,
                'bind_host': server_config['host'],
                'bind_port': server_config['port'],
                'hostname': socket.gethostname(),
                'pid': os.getpid(),
                'cluster_nodes': self._get_cluster_nodes_config()
            })
            
            self.logger.info(f"🌐 Cluster node configured: {local_ip}:{server_config['port']}")
            
        except Exception as e:
            self.logger.warning(f"Failed to setup cluster networking: {e}")
    
    def _setup_cluster_monitoring(self):
        """Setup cluster-specific monitoring"""
        try:
            from .logging.monitoring import get_memory_usage
            
            # Get initial memory usage for cluster monitoring
            memory_info = get_memory_usage()
            
            self.cluster_info.update({
                'initial_memory': memory_info,
                'monitoring_enabled': True
            })
            
            self.logger.info("📊 Cluster memory monitoring enabled")
            
        except Exception as e:
            self.logger.warning(f"Failed to setup cluster monitoring: {e}")
    
    def get_cluster_info(self) -> Dict[str, Any]:
        """Get cluster information"""
        return self.cluster_info.copy()
    
    def get_cluster_memory_details(self) -> Dict[str, Any]:
        """Get detailed cluster memory information"""
        if not self.is_cluster_mode():
            return {}
            
        try:
            from .logging.monitoring import get_memory_usage
            
            current_memory = get_memory_usage()
            initial_memory = self.cluster_info.get('initial_memory', {})
            
            memory_details = {
                'node_id': self.node_id,
                'cluster_id': self.cluster_id,
                'current_memory': current_memory,
                'initial_memory': initial_memory,
                'memory_delta': {}
            }
            
            # Calculate memory delta if initial memory is available
            if initial_memory:
                memory_details['memory_delta'] = {
                    'process_rss_mb': current_memory.get('process_rss_mb', 0) - initial_memory.get('process_rss_mb', 0),
                    'system_used_mb': current_memory.get('system_used_mb', 0) - initial_memory.get('system_used_mb', 0),
                    'system_percent': current_memory.get('system_percent', 0) - initial_memory.get('system_percent', 0)
                }
            
            return memory_details
            
        except Exception as e:
            self.logger.error(f"Failed to get cluster memory details: {e}")
            return {
                'node_id': self.node_id,
                'cluster_id': self.cluster_id,
                'error': str(e)
            }
    
    def log_cluster_shutdown_info(self):
        """Log cluster-specific information during shutdown"""
        if not self.is_cluster_mode():
            return
            
        self.logger.info("🔗 Cluster node shutting down...")
        
        # Get and log cluster memory details
        memory_details = self.get_cluster_memory_details()
        
        if 'error' not in memory_details:
            current_mem = memory_details.get('current_memory', {})
            initial_mem = memory_details.get('initial_memory', {})
            delta_mem = memory_details.get('memory_delta', {})
            
            self.logger.info(
                f"🏷️  Cluster Node: {memory_details['node_id']} "
                f"(Cluster: {memory_details['cluster_id'][:8]}...)"
            )
            
            self.logger.info(
                f"📊 Node Memory Usage: "
                f"Process RSS: {current_mem.get('process_rss_mb', 0):.2f} MB, "
                f"System: {current_mem.get('system_used_mb', 0):.2f} MB "
                f"({current_mem.get('system_percent', 0):.1f}%)"
            )
            
            if delta_mem:
                self.logger.info(
                    f"📈 Memory Delta (since startup): "
                    f"Process RSS: {delta_mem.get('process_rss_mb', 0):+.2f} MB, "
                    f"System: {delta_mem.get('system_used_mb', 0):+.2f} MB "
                    f"({delta_mem.get('system_percent', 0):+.1f}%)"
                )
        else:
            self.logger.error(f"❌ Failed to get cluster memory details: {memory_details['error']}")
        
        self.logger.info(f"🔗 Cluster node {self.node_id} shutdown complete")


# Global cluster manager instance
cluster_manager = ClusterManager()