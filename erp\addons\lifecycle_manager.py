"""
Addon Lifecycle Manager

This module provides centralized management for addon lifecycle operations,
including temporary model registry creation, cleanup, and context management.
"""
import asyncio
from typing import Dict, List, Optional, TYPE_CHECKING
from contextlib import asynccontextmanager

if TYPE_CHECKING:
    from erp.models.registry import ModelRegistry

from ..logging import get_logger


class AddonLifecycleManager:
    """
    Centralized manager for addon lifecycle operations.
    
    This manager provides:
    - Temporary model registry creation and cleanup
    - Context management for automatic cleanup
    - Thread-safe operations for concurrent addon processing
    - Status tracking for active registries
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self._active_registries: Dict[str, 'ModelRegistry'] = {}
        self._lock = asyncio.Lock()
    
    def create_temporary_model_registry(self, addon_name: str) -> 'ModelRegistry':
        """
        Create a temporary model registry for an addon.
        
        Args:
            addon_name: Name of the addon to create registry for
            
        Returns:
            ModelRegistry instance for the addon
        """
        try:
            # Import here to avoid circular imports
            from erp.models.registry import ModelRegistry

            registry = ModelRegistry(addon_name)
            self._active_registries[addon_name] = registry

            self.logger.debug(f"Created temporary model registry for addon: {addon_name}")
            return registry

        except Exception as e:
            self.logger.error(f"Failed to create temporary model registry for {addon_name}: {e}")
            raise
    
    def cleanup_temporary_model_registry(self, addon_name: str) -> bool:
        """
        Clean up a temporary model registry for an addon.
        
        Args:
            addon_name: Name of the addon to cleanup registry for
            
        Returns:
            True if cleanup successful, False otherwise
        """
        try:
            if addon_name in self._active_registries:
                registry = self._active_registries[addon_name]
                registry.clear()
                del self._active_registries[addon_name]
                
                self.logger.debug(f"Cleaned up temporary model registry for addon: {addon_name}")
                return True
            else:
                self.logger.warning(f"No active registry found for addon: {addon_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to cleanup temporary model registry for {addon_name}: {e}")
            return False
    
    def get_active_registry(self, addon_name: str) -> Optional['ModelRegistry']:
        """
        Get the active registry for an addon.
        
        Args:
            addon_name: Name of the addon
            
        Returns:
            ModelRegistry instance if active, None otherwise
        """
        return self._active_registries.get(addon_name)
    
    def has_active_registry(self, addon_name: str) -> bool:
        """
        Check if an addon has an active registry.
        
        Args:
            addon_name: Name of the addon
            
        Returns:
            True if addon has active registry, False otherwise
        """
        return addon_name in self._active_registries
    
    def get_active_addon_names(self) -> List[str]:
        """
        Get list of addon names with active registries.
        
        Returns:
            List of addon names
        """
        return list(self._active_registries.keys())
    
    def get_status_report(self) -> Dict[str, any]:
        """
        Get status report of active registries.
        
        Returns:
            Dictionary containing status information
        """
        return {
            'active_registries': len(self._active_registries),
            'addon_names': self.get_active_addon_names(),
            'registries': {
                name: {
                    'models_count': len(registry.all()) if hasattr(registry, 'all') else 0
                }
                for name, registry in self._active_registries.items()
            }
        }
    
    @asynccontextmanager
    async def temporary_registry_context(self, addon_name: str):
        """
        Context manager for temporary model registry with automatic cleanup.
        
        Args:
            addon_name: Name of the addon
            
        Yields:
            ModelRegistry instance for the addon
        """
        async with self._lock:
            registry = self.create_temporary_model_registry(addon_name)
        
        try:
            yield registry
        finally:
            async with self._lock:
                self.cleanup_temporary_model_registry(addon_name)


# Global instance
_global_lifecycle_manager: Optional[AddonLifecycleManager] = None


def get_addon_lifecycle_manager() -> AddonLifecycleManager:
    """
    Get the global addon lifecycle manager instance.
    
    Returns:
        AddonLifecycleManager singleton instance
    """
    global _global_lifecycle_manager
    
    if _global_lifecycle_manager is None:
        _global_lifecycle_manager = AddonLifecycleManager()
    
    return _global_lifecycle_manager


def reset_addon_lifecycle_manager():
    """
    Reset the global addon lifecycle manager.
    
    This is primarily used for testing to ensure clean state.
    """
    global _global_lifecycle_manager
    _global_lifecycle_manager = None



