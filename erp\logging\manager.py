"""
Logging Manager - Central logging configuration and management
"""
import logging
import logging.handlers
import os
import sys
from typing import Dict, Any, Optional, List
from pathlib import Path
import json

from .formatters import (
    ERP<PERSON>ormatter, JSONFormatter, ColoredFormatter,
    PerformanceFormatter, DatabaseFormatter, SecurityFormatter
)
from .handlers import (
    SafeConsoleHandler, RotatingFileHandler, TimedRotatingFileHandler, DatabaseHandler
)
from .filters import LevelFilter, ModuleFilter, PerformanceFilter, DuplicateFilter


class LoggingManager:
    """Central logging manager for the ERP system"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.loggers: Dict[str, logging.Logger] = {}
        self.handlers: Dict[str, logging.Handler] = {}
        self.formatters: Dict[str, logging.Formatter] = {}
        self.filters: Dict[str, logging.Filter] = {}
        self._initialized = False
        
    def initialize(self, config: Dict[str, Any]):
        """Initialize logging system with configuration"""
        if self._initialized:
            return
            
        self.config = config
        self._setup_formatters()
        self._setup_filters()
        self._setup_handlers()
        self._setup_loggers()
        self._setup_root_logger()
        self._initialized = True
        
    def _setup_formatters(self):
        """Setup logging formatters with predefined beautiful formats"""
        # Default ERP formatter with enhanced spacing and context
        self.formatters['default'] = ERPFormatter(
            include_context=True
        )

        # JSON formatter for structured logging
        if self.config.get('log_json_enabled', False):
            self.formatters['json'] = JSONFormatter(
                include_extra=self.config.get('log_json_include_extra', True)
            )

        # Enhanced colored formatter for console
        if self.config.get('log_colored_console', True):
            self.formatters['colored'] = ColoredFormatter()

        # Specialized formatters for different log types
        self.formatters['performance'] = PerformanceFormatter()
        self.formatters['database'] = DatabaseFormatter()
        self.formatters['security'] = SecurityFormatter()
            
    def _setup_filters(self):
        """Setup logging filters"""
        # Level filter
        if self.config.get('log_level_filter_enabled', False):
            min_level = self.config.get('log_level_filter_min', 'INFO')
            max_level = self.config.get('log_level_filter_max', 'CRITICAL')
            self.filters['level'] = LevelFilter(min_level, max_level)
            
        # Module filter
        if self.config.get('log_module_filter_enabled', False):
            include_modules = self.config.get('log_module_filter_include', [])
            exclude_modules = self.config.get('log_module_filter_exclude', [])
            self.filters['module'] = ModuleFilter(include_modules, exclude_modules)
            
        # Performance filter
        if self.config.get('log_performance_filter_enabled', False):
            threshold = self.config.get('log_performance_threshold', 1.0)
            self.filters['performance'] = PerformanceFilter(threshold)

        # Duplicate filter (enabled by default)
        if self.config.get('log_duplicate_filter_enabled', True):
            max_duplicates = self.config.get('log_duplicate_max', 2)
            time_window = self.config.get('log_duplicate_window', 30.0)
            self.filters['duplicate'] = DuplicateFilter(max_duplicates, time_window)
            
    def _setup_handlers(self):
        """Setup logging handlers"""
        # Console handler with safe Unicode handling
        if self.config.get('log_console_enabled', True):
            console_handler = SafeConsoleHandler(sys.stdout)
            console_handler.setLevel(
                getattr(logging, self.config.get('log_console_level', 'INFO').upper())
            )

            # Use colored formatter for console if available
            formatter_name = 'colored' if 'colored' in self.formatters else 'default'
            console_handler.setFormatter(self.formatters[formatter_name])

            # Add filters
            for filter_name in self.config.get('log_console_filters', []):
                if filter_name in self.filters:
                    console_handler.addFilter(self.filters[filter_name])

            self.handlers['console'] = console_handler
            
        # File handler
        if self.config.get('log_file_enabled', True):
            log_file = self.config.get('log_file', 'erp.log')
            log_dir = os.path.dirname(log_file) if os.path.dirname(log_file) else 'logs'
            
            # Create log directory if it doesn't exist
            Path(log_dir).mkdir(parents=True, exist_ok=True)
            
            # Choose file handler type
            if self.config.get('log_file_rotating', True):
                max_bytes = self.config.get('log_file_max_bytes', 10 * 1024 * 1024)  # 10MB
                backup_count = self.config.get('log_file_backup_count', 5)
                file_handler = RotatingFileHandler(
                    log_file, maxBytes=max_bytes, backupCount=backup_count
                )
            elif self.config.get('log_file_timed_rotating', False):
                when = self.config.get('log_file_when', 'midnight')
                interval = self.config.get('log_file_interval', 1)
                backup_count = self.config.get('log_file_backup_count', 7)
                file_handler = TimedRotatingFileHandler(
                    log_file, when=when, interval=interval, backupCount=backup_count
                )
            else:
                file_handler = logging.FileHandler(log_file)
                
            file_handler.setLevel(
                getattr(logging, self.config.get('log_file_level', 'DEBUG').upper())
            )
            
            # Use JSON formatter for file if enabled
            formatter_name = 'json' if self.config.get('log_file_json', False) else 'default'
            file_handler.setFormatter(self.formatters[formatter_name])
            
            # Add filters
            for filter_name in self.config.get('log_file_filters', []):
                if filter_name in self.filters:
                    file_handler.addFilter(self.filters[filter_name])
                    
            self.handlers['file'] = file_handler
            
        # Database handler
        if self.config.get('log_database_enabled', False):
            db_handler = DatabaseHandler(
                table_name=self.config.get('log_database_table', 'system_logs'),
                max_buffer_size=self.config.get('log_database_buffer_size', 100)
            )
            db_handler.setLevel(
                getattr(logging, self.config.get('log_database_level', 'WARNING').upper())
            )
            db_handler.setFormatter(self.formatters['json'])
            
            self.handlers['database'] = db_handler
            
    def _setup_loggers(self):
        """Setup specific loggers"""
        # Setup module-specific loggers
        logger_configs = self.config.get('loggers', {})
        for logger_name, logger_config in logger_configs.items():
            logger = logging.getLogger(logger_name)
            logger.setLevel(
                getattr(logging, logger_config.get('level', 'INFO').upper())
            )
            
            # Add specific handlers for this logger
            for handler_name in logger_config.get('handlers', []):
                if handler_name in self.handlers:
                    logger.addHandler(self.handlers[handler_name])
                    
            # Prevent propagation if specified
            logger.propagate = logger_config.get('propagate', True)
            
            self.loggers[logger_name] = logger
            
    def _setup_root_logger(self):
        """Setup root logger and configure third-party loggers"""
        root_logger = logging.getLogger()
        root_logger.setLevel(
            getattr(logging, self.config.get('log_level', 'INFO').upper())
        )

        # Clear existing handlers
        root_logger.handlers.clear()

        # Add configured handlers
        for handler_name in self.config.get('log_handlers', ['console', 'file']):
            if handler_name in self.handlers:
                root_logger.addHandler(self.handlers[handler_name])

        # Configure third-party loggers to use our formatting
        self._configure_third_party_loggers()

    def _configure_third_party_loggers(self):
        """Configure third-party loggers to use consistent ERP formatting"""
        # List of third-party loggers to configure
        third_party_loggers = [
            'uvicorn',
            'uvicorn.access',
            'uvicorn.error',
            'fastapi',
            'asyncpg',
            'sqlalchemy',
            'httpx',
            'aiofiles',
        ]

        # Get the console handler for consistent formatting
        console_handler = self.handlers.get('console')
        if not console_handler:
            return

        for logger_name in third_party_loggers:
            try:
                logger = logging.getLogger(logger_name)

                # Clear existing handlers to avoid duplicates
                logger.handlers.clear()

                # Add our console handler
                logger.addHandler(console_handler)

                # Set appropriate log level
                if logger_name.startswith('uvicorn'):
                    # Uvicorn loggers should respect our log level but not be too verbose
                    min_level = max(
                        getattr(logging, self.config.get('log_level', 'INFO').upper()),
                        logging.INFO
                    )
                    logger.setLevel(min_level)
                else:
                    # Other third-party loggers use our global level
                    logger.setLevel(
                        getattr(logging, self.config.get('log_level', 'INFO').upper())
                    )

                # Prevent propagation to avoid duplicate messages
                logger.propagate = False

            except Exception as e:
                # Don't fail if we can't configure a third-party logger
                pass

    def get_logger(self, name: str) -> logging.Logger:
        """Get or create a logger with the given name"""
        if name in self.loggers:
            return self.loggers[name]
            
        logger = logging.getLogger(name)
        self.loggers[name] = logger
        return logger
        
    def add_handler(self, name: str, handler: logging.Handler):
        """Add a custom handler"""
        self.handlers[name] = handler
        
    def add_formatter(self, name: str, formatter: logging.Formatter):
        """Add a custom formatter"""
        self.formatters[name] = formatter
        
    def add_filter(self, name: str, filter_obj: logging.Filter):
        """Add a custom filter"""
        self.filters[name] = filter_obj
        
    def configure_uvicorn_logging(self):
        """Configure uvicorn logging to use our formatters"""
        try:
            # Get our console handler
            console_handler = self.handlers.get('console')
            if not console_handler:
                return

            # Configure uvicorn access logger
            uvicorn_access = logging.getLogger("uvicorn.access")
            uvicorn_access.handlers.clear()
            uvicorn_access.addHandler(console_handler)
            uvicorn_access.propagate = False

            # Configure uvicorn error logger
            uvicorn_error = logging.getLogger("uvicorn.error")
            uvicorn_error.handlers.clear()
            uvicorn_error.addHandler(console_handler)
            uvicorn_error.propagate = False

            # Configure main uvicorn logger
            uvicorn_logger = logging.getLogger("uvicorn")
            uvicorn_logger.handlers.clear()
            uvicorn_logger.addHandler(console_handler)
            uvicorn_logger.propagate = False

        except Exception:
            # Don't fail if uvicorn logging configuration fails
            pass

    def shutdown(self):
        """Shutdown logging system"""
        logging.shutdown()


# Global logging manager instance
_logging_manager: Optional[LoggingManager] = None


def get_logging_manager() -> LoggingManager:
    """Get the global logging manager instance"""
    global _logging_manager
    if _logging_manager is None:
        _logging_manager = LoggingManager()
    return _logging_manager


def get_logger(name: str) -> logging.Logger:
    """Get a logger with the given name"""
    return get_logging_manager().get_logger(name)


def initialize_logging(config: Dict[str, Any]):
    """Initialize the logging system with configuration"""
    get_logging_manager().initialize(config)


def test_logging_colors():
    """Test function to verify all log levels display with proper colors"""
    logger = get_logger("erp.logging.test")

    logger.debug("🔍 This is a DEBUG message - should appear in bright cyan")
    logger.info("ℹ️ This is an INFO message - should appear in bright green")
    logger.warning("⚠️ This is a WARNING message - should appear in bright yellow")
    logger.error("❌ This is an ERROR message - should appear in bright red")
    logger.critical("🚨 This is a CRITICAL message - should appear in white on red background")

    # Test with context
    import logging
    record = logging.LogRecord(
        name="erp.test.context",
        level=logging.INFO,
        pathname="",
        lineno=0,
        msg="Test message with context",
        args=(),
        exc_info=None
    )
    record.database = "test_db"
    record.user_id = "admin"
    record.request_id = "req123456789"
    record.duration = 1.234

    logger.handle(record)
