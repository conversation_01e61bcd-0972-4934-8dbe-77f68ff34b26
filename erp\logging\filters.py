"""
Logging Filters - Custom filters for log record processing
"""
import logging
import re
import time
from typing import List, Optional, Union, Pattern


class LevelFilter(logging.Filter):
    """Filter log records by level range"""
    
    def __init__(self, min_level: Union[str, int] = 'DEBUG', max_level: Union[str, int] = 'CRITICAL'):
        super().__init__()
        
        # Convert string levels to integers
        if isinstance(min_level, str):
            self.min_level = getattr(logging, min_level.upper())
        else:
            self.min_level = min_level
            
        if isinstance(max_level, str):
            self.max_level = getattr(logging, max_level.upper())
        else:
            self.max_level = max_level
            
    def filter(self, record: logging.LogRecord) -> bool:
        """Filter record based on level range"""
        return self.min_level <= record.levelno <= self.max_level


class ModuleFilter(logging.Filter):
    """Filter log records by module name patterns"""
    
    def __init__(self, include_patterns: Optional[List[str]] = None, 
                 exclude_patterns: Optional[List[str]] = None):
        super().__init__()
        
        # Compile include patterns
        self.include_patterns: List[Pattern] = []
        if include_patterns:
            for pattern in include_patterns:
                self.include_patterns.append(re.compile(pattern))
                
        # Compile exclude patterns
        self.exclude_patterns: List[Pattern] = []
        if exclude_patterns:
            for pattern in exclude_patterns:
                self.exclude_patterns.append(re.compile(pattern))
                
    def filter(self, record: logging.LogRecord) -> bool:
        """Filter record based on module name patterns"""
        module_name = record.name
        
        # Check exclude patterns first
        for pattern in self.exclude_patterns:
            if pattern.search(module_name):
                return False
                
        # If no include patterns, allow all (that weren't excluded)
        if not self.include_patterns:
            return True
            
        # Check include patterns
        for pattern in self.include_patterns:
            if pattern.search(module_name):
                return True
                
        return False


class PerformanceFilter(logging.Filter):
    """Filter log records based on performance thresholds"""
    
    def __init__(self, min_duration: float = 0.0, max_duration: Optional[float] = None):
        super().__init__()
        self.min_duration = min_duration
        self.max_duration = max_duration
        
    def filter(self, record: logging.LogRecord) -> bool:
        """Filter record based on performance duration"""
        # Only filter records that have duration information
        if not hasattr(record, 'duration'):
            return True
            
        duration = record.duration
        
        # Check minimum duration
        if duration < self.min_duration:
            return False
            
        # Check maximum duration
        if self.max_duration is not None and duration > self.max_duration:
            return False
            
        return True


class RateLimitFilter(logging.Filter):
    """Filter log records based on rate limiting"""
    
    def __init__(self, max_records: int = 100, time_window: float = 60.0):
        super().__init__()
        self.max_records = max_records
        self.time_window = time_window
        self.records = []
        
    def filter(self, record: logging.LogRecord) -> bool:
        """Filter record based on rate limiting"""
        current_time = time.time()
        
        # Remove old records outside the time window
        self.records = [t for t in self.records if current_time - t <= self.time_window]
        
        # Check if we're under the limit
        if len(self.records) < self.max_records:
            self.records.append(current_time)
            return True
            
        return False


class DuplicateFilter(logging.Filter):
    """Filter duplicate log records"""
    
    def __init__(self, max_duplicates: int = 2, time_window: float = 30.0):
        super().__init__()
        self.max_duplicates = max_duplicates
        self.time_window = time_window
        self.message_counts = {}
        
    def filter(self, record: logging.LogRecord) -> bool:
        """Filter duplicate records"""
        current_time = time.time()
        message_key = f"{record.name}:{record.levelno}:{record.getMessage()}"
        
        # Clean old entries
        for key in list(self.message_counts.keys()):
            timestamps = self.message_counts[key]
            # Remove timestamps outside the time window
            timestamps = [t for t in timestamps if current_time - t <= self.time_window]
            if timestamps:
                self.message_counts[key] = timestamps
            else:
                del self.message_counts[key]
                
        # Check duplicate count
        if message_key not in self.message_counts:
            self.message_counts[message_key] = []
            
        timestamps = self.message_counts[message_key]
        
        if len(timestamps) < self.max_duplicates:
            timestamps.append(current_time)
            return True
        else:
            # Suppress further duplicates silently
            return False


class SecurityFilter(logging.Filter):
    """Filter for security-related log records"""
    
    def __init__(self, security_levels: Optional[List[str]] = None):
        super().__init__()
        self.security_levels = security_levels or ['WARNING', 'ERROR', 'CRITICAL']
        self.security_level_nums = [getattr(logging, level) for level in self.security_levels]
        
    def filter(self, record: logging.LogRecord) -> bool:
        """Filter security-related records"""
        # Check if record has security context
        has_security_context = any([
            hasattr(record, 'security_event'),
            hasattr(record, 'user_id'),
            hasattr(record, 'client_ip'),
            'security' in record.name.lower(),
            'auth' in record.name.lower(),
            'login' in record.getMessage().lower(),
            'permission' in record.getMessage().lower(),
        ])
        
        # Only pass security-related records of appropriate levels
        return (has_security_context and 
                record.levelno in self.security_level_nums)


class DatabaseFilter(logging.Filter):
    """Filter for database-related log records"""
    
    def __init__(self, include_queries: bool = False, min_query_time: float = 0.1):
        super().__init__()
        self.include_queries = include_queries
        self.min_query_time = min_query_time
        
    def filter(self, record: logging.LogRecord) -> bool:
        """Filter database-related records"""
        # Check if record is database-related
        is_db_related = any([
            hasattr(record, 'query'),
            hasattr(record, 'database'),
            'database' in record.name.lower(),
            'db' in record.name.lower(),
            'sql' in record.getMessage().lower(),
        ])
        
        if not is_db_related:
            return True
            
        # Filter queries if not included
        if hasattr(record, 'query') and not self.include_queries:
            return False
            
        # Filter by query execution time
        if hasattr(record, 'query_time'):
            return record.query_time >= self.min_query_time
            
        return True


class ContextFilter(logging.Filter):
    """Filter that adds context information to log records"""
    
    def __init__(self, context_fields: Optional[List[str]] = None):
        super().__init__()
        self.context_fields = context_fields or ['user_id', 'database', 'request_id']
        
    def filter(self, record: logging.LogRecord) -> bool:
        """Add context information to record"""
        # Try to get context from various sources
        try:
            # This would integrate with the ERP context system
            from ..context import ContextManager
            context = ContextManager.get_current_context()
            
            if context:
                for field in self.context_fields:
                    if hasattr(context, field) and not hasattr(record, field):
                        setattr(record, field, getattr(context, field))
                        
        except Exception:
            # Don't fail if context is not available
            pass
            
        return True


class SensitiveDataFilter(logging.Filter):
    """Filter that removes or masks sensitive data from log records"""
    
    def __init__(self, sensitive_patterns: Optional[List[str]] = None):
        super().__init__()
        
        # Default sensitive data patterns
        default_patterns = [
            r'password["\s]*[:=]["\s]*[^"\s]+',
            r'token["\s]*[:=]["\s]*[^"\s]+',
            r'secret["\s]*[:=]["\s]*[^"\s]+',
            r'key["\s]*[:=]["\s]*[^"\s]+',
            r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b',  # Credit card numbers
            r'\b\d{3}-\d{2}-\d{4}\b',  # SSN
        ]
        
        patterns = sensitive_patterns or default_patterns
        self.sensitive_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in patterns]
        
    def filter(self, record: logging.LogRecord) -> bool:
        """Mask sensitive data in record"""
        # Get the message
        message = record.getMessage()
        
        # Apply masking to message
        for pattern in self.sensitive_patterns:
            message = pattern.sub('[MASKED]', message)
            
        # Update the record
        record.msg = message
        record.args = ()
        
        return True
