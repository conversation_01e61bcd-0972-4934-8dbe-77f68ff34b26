"""
Test suite for HTTP routing functionality

This module tests:
- Route decorator functionality
- RouteRegistry operations
- Route registration and retrieval
- Route configuration and parameters
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

from erp.http import (
    route, RouteRegistry, RouteType, HttpMethod,
    AuthType
)


class TestRouteDecorator:
    """Test HTTP route decorator functionality"""

    def test_route_decorator_basic(self):
        """Test basic route decorator usage"""
        @route('/test', methods=['GET'])
        async def test_handler(request):
            return {'message': 'test'}

        # Check that function is decorated properly
        assert hasattr(test_handler, '_route_info')
        route_info = test_handler._route_info
        assert route_info['path'] == '/test'
        assert route_info['methods'] == ['GET']
        assert route_info['type'] == RouteType.HTTP

    def test_route_decorator_json_type(self):
        """Test route decorator with JSON type"""
        @route('/api/test', type=RouteType.JSON, methods=['POST'])
        async def json_handler(request):
            return {'result': 'success'}

        route_info = json_handler._route_info
        assert route_info['type'] == RouteType.JSON
        assert route_info['methods'] == ['POST']

    def test_route_decorator_auth_settings(self):
        """Test route decorator with authentication settings"""
        @route('/admin', auth=AuthType.ADMIN, methods=['GET'])
        async def admin_handler(request):
            return {'admin': True}

        route_info = admin_handler._route_info
        assert route_info['auth'] == AuthType.ADMIN

    def test_route_decorator_database_specific(self):
        """Test route decorator with database-specific routing"""
        @route('/db/info', database='test_db', methods=['GET'])
        async def db_handler(request):
            return {'database': 'test_db'}

        route_info = db_handler._route_info
        assert route_info['database'] == 'test_db'

    def test_route_decorator_default_methods(self):
        """Test route decorator default methods"""
        @route('/default')
        async def default_handler(request):
            return {'default': True}

        route_info = default_handler._route_info
        assert route_info['methods'] == ['GET']  # Default for HTTP type

        @route('/json_default', type=RouteType.JSON)
        async def json_default_handler(request):
            return {'json': True}

        json_route_info = json_default_handler._route_info
        assert json_route_info['methods'] == ['POST']  # Default for JSON type


class TestRouteRegistry:
    """Test RouteRegistry functionality"""

    def test_route_registry_initialization(self):
        """Test RouteRegistry initialization"""
        registry = RouteRegistry()

        assert hasattr(registry, '_routes')
        assert isinstance(registry._routes, dict)

    def test_route_registry_register(self):
        """Test route registration"""
        registry = RouteRegistry()

        async def test_handler(request):
            return {'test': True}

        registry.register('/test', test_handler, methods=['GET'], type=RouteType.HTTP)

        routes = registry.get_routes()
        assert '/test' in routes
        assert routes['/test']['handler'] == test_handler
        assert routes['/test']['methods'] == ['GET']

    def test_route_registry_get_routes(self):
        """Test getting all routes from registry"""
        registry = RouteRegistry()

        async def handler1(request):
            return {'1': True}

        async def handler2(request):
            return {'2': True}

        registry.register('/route1', handler1)
        registry.register('/route2', handler2)

        routes = registry.get_routes()
        assert len(routes) == 2
        assert '/route1' in routes
        assert '/route2' in routes

    def test_route_registry_clear(self):
        """Test clearing routes from registry"""
        registry = RouteRegistry()

        async def test_handler(request):
            return {'test': True}

        registry.register('/test', test_handler)
        assert len(registry.get_routes()) == 1

        registry.clear()
        assert len(registry.get_routes()) == 0
