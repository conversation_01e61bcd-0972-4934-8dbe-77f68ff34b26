"""
Relational field types for ERP models
"""
from .base import RelationalField, FieldValidationError


class Many2One(RelationalField):
    """Many-to-one relationship field (foreign key)"""

    def __init__(self, comodel_name, ondelete='set null', **kwargs):
        """
        Initialize Many2One field

        Args:
            comodel_name: Name of the related model
            ondelete: Action when referenced record is deleted
                     ('cascade', 'set null', 'restrict')
        """
        super().__init__(comodel_name, **kwargs)
        self.ondelete = ondelete

    def get_sql_type(self):
        """Get SQL type for foreign key"""
        return "UUID"  # References the id field of related model

    def _validate_value(self, value):
        """Validate Many2One field value"""
        if value is None:
            return None

        # Value can be:
        # 1. A string (UUID of the related record)
        # 2. A model instance
        # 3. A RecordSet with single record

        if isinstance(value, str):
            # Validate UUID format
            import uuid
            try:
                uuid.UUID(value)
                return value
            except ValueError:
                raise FieldValidationError(f"Invalid UUID format: {value}")



        elif hasattr(value, 'id'):
            # Model instance - get its ID
            return value.id

        elif hasattr(value, '_records') and len(value._records) == 1:
            # RecordSet with single record
            return value._records[0].id

        elif hasattr(value, '_records') and len(value._records) == 0:
            # Empty RecordSet
            return None

        elif hasattr(value, '_records') and len(value._records) > 1:
            # RecordSet with multiple records
            raise FieldValidationError("Cannot assign multiple records to Many2One field")

        else:
            raise FieldValidationError(f"Invalid value type for Many2One field: {type(value)}")

    def convert_to_cache(self, value):
        """Convert value for caching - store the ID"""
        if value is None:
            return None
        return str(value)

    def convert_to_record(self, value):
        """Convert cached value back to record value"""
        if value is None:
            return None
        # This should return a RecordSet, but for now return the ID
        # Will be implemented when we have proper model registry
        return value


class One2Many(RelationalField):
    """One-to-many relationship field (inverse of Many2One)"""

    def __init__(self, comodel_name, inverse_name, **kwargs):
        """
        Initialize One2Many field

        Args:
            comodel_name: Name of the related model
            inverse_name: Name of the Many2One field in the related model
                         that points back to this model
        """
        super().__init__(comodel_name, **kwargs)
        self.inverse_name = inverse_name
        # One2Many fields are not stored in database directly
        self.store = False

    def get_sql_type(self):
        """One2Many fields don't have SQL representation"""
        return None

    def _validate_value(self, value):
        """Validate One2Many field value"""
        if value is None:
            return None

        # Value can be:
        # 1. A list of IDs
        # 2. A list of model instances
        # 3. A RecordSet
        # 4. Command tuples for operations

        if isinstance(value, list):
            # Handle command tuples or list of records/IDs
            validated_commands = []
            for item in value:
                if isinstance(item, tuple) and len(item) >= 2:
                    # Command tuple: (command, id, values)
                    command = item[0]
                    if command in (0, 1, 2, 3, 4, 5, 6):  # Valid command codes
                        validated_commands.append(item)
                    else:
                        raise FieldValidationError(f"Invalid command code: {command}")
                elif isinstance(item, str):
                    # ID string
                    validated_commands.append((4, item, 0))  # Link command
                elif isinstance(item, int):
                    # ID integer
                    validated_commands.append((4, str(item), 0))  # Link command
                elif hasattr(item, 'id'):
                    # Model instance
                    validated_commands.append((4, item.id, 0))  # Link command
                else:
                    raise FieldValidationError(f"Invalid item type in One2Many list: {type(item)}")
            return validated_commands

        elif hasattr(value, '_records'):
            # RecordSet - convert to link commands
            return [(4, record.id, 0) for record in value._records]

        else:
            raise FieldValidationError(f"Invalid value type for One2Many field: {type(value)}")

    def convert_to_cache(self, value):
        """Convert value for caching"""
        if value is None:
            return []
        # Store as list of IDs
        if isinstance(value, list):
            ids = []
            for item in value:
                if isinstance(item, tuple) and len(item) >= 2:
                    command, record_id = item[0], item[1]
                    if command in (4, 1):  # Link or update
                        ids.append(record_id)
                    # Skip other commands for cache
                else:
                    ids.append(str(item))
            return ids
        return []

    def convert_to_record(self, value):
        """Convert cached value back to record value"""
        if not value:
            return []
        # This should return a RecordSet, but for now return list of IDs
        # Will be implemented when we have proper model registry
        return value


class Many2Many(RelationalField):
    """Many-to-many relationship field with intersection table"""

    def __init__(self, comodel_name, relation=None, column1=None, column2=None, **kwargs):
        """
        Initialize Many2Many field

        Args:
            comodel_name: Name of the related model
            relation: Name of the intersection table (auto-generated if not provided)
            column1: Name of the column referencing this model (auto-generated if not provided)
            column2: Name of the column referencing the comodel (auto-generated if not provided)
        """
        super().__init__(comodel_name, **kwargs)
        self.relation_table = relation
        self.column1 = column1
        self.column2 = column2
        # Many2Many fields are not stored in main table
        self.store = False

    def get_relation_table_name(self, model_name):
        """Generate intersection table name"""
        if self.relation_table:
            return self.relation_table

        # Auto-generate table name: model1_model2_rel
        model1 = model_name.replace('.', '_')
        model2 = self.comodel_name.replace('.', '_')

        # Ensure consistent ordering for bidirectional relationships
        if model1 < model2:
            return f"{model1}_{model2}_rel"
        else:
            return f"{model2}_{model1}_rel"

    def get_column_names(self, model_name):
        """Get column names for intersection table"""
        if self.column1 and self.column2:
            return self.column1, self.column2

        # Auto-generate column names
        model1_col = model_name.replace('.', '_') + '_id'
        model2_col = self.comodel_name.replace('.', '_') + '_id'

        return model1_col, model2_col

    def get_sql_type(self):
        """Many2Many fields don't have SQL representation in main table"""
        return None

    def get_intersection_table_schema(self, model_name):
        """Get schema for intersection table"""
        table_name = self.get_relation_table_name(model_name)
        col1, col2 = self.get_column_names(model_name)

        return {
            'table_name': table_name,
            'columns': {
                col1: 'UUID NOT NULL',
                col2: 'UUID NOT NULL'
            },
            'constraints': [
                f'PRIMARY KEY ({col1}, {col2})',
                f'FOREIGN KEY ({col1}) REFERENCES {model_name.replace(".", "_")} (id) ON DELETE CASCADE',
                f'FOREIGN KEY ({col2}) REFERENCES {self.comodel_name.replace(".", "_")} (id) ON DELETE CASCADE'
            ],
            'indexes': [
                f'CREATE INDEX IF NOT EXISTS idx_{table_name}_{col1} ON {table_name} ({col1})',
                f'CREATE INDEX IF NOT EXISTS idx_{table_name}_{col2} ON {table_name} ({col2})'
            ]
        }

    def _validate_value(self, value):
        """Validate Many2Many field value"""
        if value is None:
            return None

        # Value can be:
        # 1. A list of IDs
        # 2. A list of model instances
        # 3. A RecordSet
        # 4. Command tuples for operations

        if isinstance(value, list):
            # Handle command tuples or list of records/IDs
            validated_commands = []
            for item in value:
                if isinstance(item, tuple) and len(item) >= 2:
                    # Command tuple: (command, id, values)
                    command = item[0]
                    if command in (0, 1, 2, 3, 4, 5, 6):  # Valid command codes
                        validated_commands.append(item)
                    else:
                        raise FieldValidationError(f"Invalid command code: {command}")
                elif isinstance(item, str):
                    # ID string
                    validated_commands.append((4, item, 0))  # Link command
                elif isinstance(item, int):
                    # ID integer
                    validated_commands.append((4, str(item), 0))  # Link command
                elif hasattr(item, 'id'):
                    # Model instance
                    validated_commands.append((4, item.id, 0))  # Link command
                else:
                    raise FieldValidationError(f"Invalid item type in Many2Many list: {type(item)}")
            return validated_commands

        elif hasattr(value, '_records'):
            # RecordSet - convert to link commands
            return [(4, record.id, 0) for record in value._records]

        else:
            raise FieldValidationError(f"Invalid value type for Many2Many field: {type(value)}")

    def convert_to_cache(self, value):
        """Convert value for caching"""
        if value is None:
            return []
        # Store as list of IDs
        if isinstance(value, list):
            ids = []
            for item in value:
                if isinstance(item, tuple) and len(item) >= 2:
                    command, record_id = item[0], item[1]
                    if command in (4, 1):  # Link or update
                        ids.append(record_id)
                    # Skip other commands for cache
                else:
                    ids.append(str(item))
            return ids
        return []

    def convert_to_record(self, value):
        """Convert cached value back to record value"""
        if not value:
            return []
        # This should return a RecordSet, but for now return list of IDs
        # Will be implemented when we have proper model registry
        return value


class One2One(Many2One):
    """One-to-one relationship field (unique foreign key)"""

    def __init__(self, comodel_name, inverse_name=None, **kwargs):
        """
        Initialize One2One field

        Args:
            comodel_name: Name of the related model
            inverse_name: Name of the inverse One2One field in the related model
        """
        super().__init__(comodel_name, **kwargs)
        self.inverse_name = inverse_name
        # One2One fields should have unique constraint
        self.unique = True

    def get_sql_constraints(self, field_name, table_name=None):
        """Get SQL constraints for One2One field"""
        constraints = []

        # Add unique constraint
        constraints.append(f'UNIQUE ({field_name})')

        # Add foreign key constraint
        referenced_table = self.comodel_name.replace('.', '_')
        constraints.append(
            f'FOREIGN KEY ({field_name}) REFERENCES {referenced_table} (id) '
            f'ON DELETE {self._get_sql_ondelete()}'
        )

        return constraints

    def _get_sql_ondelete(self):
        """Convert ondelete action to SQL"""
        ondelete_map = {
            'cascade': 'CASCADE',
            'set null': 'SET NULL',
            'restrict': 'RESTRICT',
            'no action': 'NO ACTION'
        }
        return ondelete_map.get(self.ondelete.lower(), 'SET NULL')


class Related(RelationalField):
    """Related field - computed field that follows relationships"""

    def __init__(self, related, **kwargs):
        """
        Initialize Related field

        Args:
            related: Dot-separated path to the related field (e.g., 'partner_id.name')
        """
        # Related fields are computed and not stored by default
        kwargs.setdefault('compute', True)
        kwargs.setdefault('store', False)
        kwargs.setdefault('readonly', True)

        # Extract comodel_name from the related path (first part)
        related_path = related.split('.')
        if len(related_path) > 1:
            # For now, we can't determine the comodel_name without model registry
            # So we'll use a placeholder
            comodel_name = 'unknown'
        else:
            comodel_name = 'unknown'

        super().__init__(comodel_name, **kwargs)
        self.related = related
        self.related_path = related_path

    def get_sql_type(self):
        """Related fields don't have SQL representation unless stored"""
        if self.store:
            # If stored, we need to determine the type from the target field
            # For now, default to TEXT
            return "TEXT"
        return None

    def _validate_value(self, value):
        """Related fields are computed, so validation depends on target field"""
        # The validation will be handled by the target field
        return value

    def compute_value(self, record):
        """Compute the related field value by following the path"""
        current = record

        try:
            for step in self.related_path:
                if hasattr(current, step):
                    current = getattr(current, step)
                else:
                    return None

                # If we hit None at any point, return None
                if current is None:
                    return None

            return current

        except (AttributeError, TypeError):
            return None

    def convert_to_cache(self, value):
        """Convert value for caching"""
        return value

    def convert_to_record(self, value):
        """Convert cached value back to record value"""
        return value


class Reference(RelationalField):
    """Reference field - can reference any model"""

    def __init__(self, selection=None, **kwargs):
        """
        Initialize Reference field

        Args:
            selection: List of (model_name, label) tuples for allowed models
        """
        # Reference fields don't have a specific comodel
        super().__init__('reference', **kwargs)
        self.selection = selection or []

    def get_sql_type(self):
        """Reference fields store model,id as text"""
        return "VARCHAR(255)"

    def _validate_value(self, value):
        """Validate Reference field value"""
        if value is None:
            return None

        # Value should be in format "model_name,id"
        if isinstance(value, str):
            if ',' in value:
                model_name, _ = value.split(',', 1)  # _ for unused record_id
                # Validate model is in selection if provided
                if self.selection:
                    valid_models = [item[0] for item in self.selection]
                    if model_name not in valid_models:
                        raise FieldValidationError(f"Invalid model '{model_name}' for Reference field")
                return value
            else:
                raise FieldValidationError("Reference value must be in format 'model,id'")

        elif hasattr(value, '_name') and hasattr(value, 'id'):
            # Model instance
            model_name = value._name
            if self.selection:
                valid_models = [item[0] for item in self.selection]
                if model_name not in valid_models:
                    raise FieldValidationError(f"Invalid model '{model_name}' for Reference field")
            return f"{model_name},{value.id}"

        else:
            raise FieldValidationError(f"Invalid value type for Reference field: {type(value)}")
