# Technical Reference

## Logging System

### Overview
The ERP system features a comprehensive logging framework with beautiful formatting, intelligent filtering, and multiple output destinations.

### Key Features
- **Beautiful Formatting**: Predefined formats with colors, icons, and proper spacing
- **Multiple Destinations**: Console, file, database, and memory logging
- **Intelligent Filtering**: Rate limiting, duplicate detection, module filtering
- **Performance Monitoring**: Built-in performance tracking and alerts
- **Security Logging**: Enhanced security event tracking
- **Smart Colors**: Automatic color detection with fallback support
- **Context Awareness**: Automatic injection of user, database, and request context

### Basic Usage
```python
from erp.logging import get_logger, log_structured

# Get a logger
logger = get_logger(__name__)

# Basic logging
logger.info("🚀 Application started successfully")
logger.warning("⚠️ Configuration file not found, using defaults")
logger.error("❌ Database connection failed")

# Structured logging with context
log_structured(
    logger, logger.INFO,
    "🔐 User login successful",
    user_id="john_doe",
    database="production_db",
    client_ip="*************",
    operation="login"
)
```

### Configuration
```ini
# erp.conf
[options]
log_level = DEBUG
log_file = logs/erp.log
```

## Memory Registry System

### Overview
The Memory Registry System provides per-database in-memory storage for models, routes, and other runtime data with intelligent filtering and management.

### Core Components

#### DatabaseMemoryRegistry
Enhanced per-database registry for storing:
- Model definitions and metadata with full field information
- Route registrations in addon installation order
- Query cache with TTL and LRU eviction
- Runtime statistics and performance metrics
- Addon loading order tracking

#### MemoryRegistryManager
Singleton managing all database registries:
- Registry creation and lifecycle
- Database filtering logic
- Cross-registry operations

#### DatabaseFilterProcessor
Handles database filtering logic:
- Pattern matching for database names
- Registry creation decisions
- Filter validation

### Usage Example
```python
from erp.database import get_memory_registry_manager

# Get manager instance
manager = get_memory_registry_manager()

# Get registry for specific database
registry = await manager.get_registry('my_database')

# Store data
await registry.set_model_data('my.model', model_definition)

# Retrieve data
model_data = await registry.get_model_data('my.model')
```

## HTTP Routing System

### Route Decorator
```python
from erp.http import route, Controller

class MyController(Controller):
    
    @route('/api/data', methods=['GET'], type='json')
    async def get_data(self, request):
        return self.json_response({'data': 'example'})
    
    @route('/page', methods=['GET'], type='http')
    async def show_page(self, request):
        context = {'title': 'My Page'}
        return self.render_template('my_template.xml', context)
```

### Database-Specific Routes
Routes can be registered per database:
```python
@route('/db_specific', methods=['GET'], database='my_db')
async def db_specific_route(self, request):
    return self.json_response({'database': 'my_db'})
```

## Model System

### Field Types
```python
from erp.fields import (
    Char, Text, Integer, Float, Boolean,
    Date, DateTime, Selection, Many2one, One2many
)

class MyModel(Model):
    _name = 'my.model'
    _description = 'My Model'
    
    name = Char(string='Name', size=100, required=True)
    description = Text(string='Description')
    count = Integer(string='Count', default=0)
    price = Float(string='Price', digits=(10, 2))
    active = Boolean(string='Active', default=True)
    
    state = Selection([
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('done', 'Done')
    ], string='State', default='draft')
```

### RecordSet Operations
```python
# Search records
records = await MyModel.search([('active', '=', True)])

# Create record
record = await MyModel.create({'name': 'Test', 'count': 5})

# Update records
await records.write({'active': False})

# Delete records
await records.unlink()

# Use mapped() for bulk operations
names = records.mapped('name')
```

## Environment System

### Environment Structure
```python
# Environment provides access to:
env.cr      # Database cursor
env.uid     # Current user ID
env.context # Request context dictionary
env.user    # Current user record
```

### Usage in Models
```python
class MyModel(Model):
    async def my_method(self):
        # Access environment
        current_user = self.env.user
        database = self.env.cr.database
        
        # Use context
        lang = self.env.context.get('lang', 'en_US')
```

### Usage in Controllers
```python
class MyController(Controller):
    @route('/api/user', methods=['GET'])
    async def get_user(self, request):
        env = request.state.env
        user = env.user
        return self.json_response({
            'user_id': user.id,
            'database': env.cr.database
        })
```

## Database Management

### Connection Configuration
```python
# Database configuration in erp.conf
[options]
db_host = localhost
db_port = 5432
db_user = erp
db_password = erp
db_name = erp_main

# Multi-database support
list_db = True
db_filter = ^(erp_.*|test_.*)$
```

### Database Registry
```python
from erp.database import DatabaseRegistry

# Get database manager
db_registry = DatabaseRegistry()

# Get database connection
async with db_registry.get_database('my_db') as db:
    # Execute queries
    result = await db.execute("SELECT * FROM my_table")
```

## Addon System

### Addon Lifecycle

#### Discovery Phase
1. Scan addon directories
2. Load manifests
3. Validate dependencies
4. Build dependency graph

#### Registration Phase
1. Manual import of Python modules
2. Manual setup of models and routes
3. Manual module initialization

#### Installation Phase (Enhanced with Dependency-Aware Savepoints)
1. **Dependency Resolution**: Recursively resolve all dependencies with proper topological sorting
2. **Installation Planning**: Create installation plan with dependency metadata
3. **Validation**: Validate installation order and check for conflicts
4. **Dependency Installation**: Install dependencies first with individual savepoints
5. **Dependency Commitment**: Commit dependency savepoints immediately upon success
6. **Main Addon Installation**: Install explicitly requested addons
7. **State Management**: Update addon states with dependency tracking
8. **Cleanup**: Clean up completed savepoints

### Enhanced Dependency-Aware Savepoint System

The new savepoint system ensures that dependencies remain installed even if main addons fail:

#### Key Features
- **Individual Savepoints**: Each addon gets its own savepoint
- **Dependency Preservation**: Dependencies are committed immediately upon successful installation
- **Rollback Isolation**: Main addon failures only rollback the main addon, not dependencies
- **Installation Tracking**: Track whether addons were installed explicitly or as dependencies
- **Conflict Detection**: Detect circular dependencies and missing dependencies

#### Installation Process Example

For addon A that depends on B and C, where B depends on D:

```
Installation Order: D → B → C → A

1. Install D (dependency):
   - Create savepoint: addon_install_D_timestamp
   - Execute D installation
   - Commit savepoint immediately (D is preserved)

2. Install B (dependency):
   - Create savepoint: addon_install_B_timestamp
   - Execute B installation
   - Commit savepoint immediately (B is preserved)

3. Install C (dependency):
   - Create savepoint: addon_install_C_timestamp
   - Execute C installation
   - Commit savepoint immediately (C is preserved)

4. Install A (explicit):
   - Create savepoint: addon_install_A_timestamp
   - Execute A installation
   - If success: Release savepoint
   - If failure: Rollback only A (D, B, C remain installed)
```

#### Savepoint States
- **CREATED**: Savepoint created but not yet committed or rolled back
- **COMMITTED**: Savepoint successfully committed (for dependencies)
- **ROLLED_BACK**: Savepoint rolled back due to failure

### Hook System
```python
from erp.addons.hooks import pre_install_hook, post_install_hook

@pre_install_hook('my_addon', priority=50)
async def pre_install(context):
    logger = context.logger
    logger.info("Pre-installing my_addon")
    return True

@post_install_hook('my_addon', priority=50)
async def post_install(context):
    env = context.env
    # Create default data
    return True
```

## Template System

### QWeb-like Templates
```xml
<templates>
    <t t-name="my_template">
        <div>
            <h1 t-esc="title"/>
            <t t-if="show_list">
                <ul>
                    <t t-foreach="items" t-as="item">
                        <li t-esc="item.name"/>
                    </t>
                </ul>
            </t>
        </div>
    </t>
</templates>
```

### Template Directives
- `t-name`: Template name
- `t-esc`: Escape and output value
- `t-raw`: Output raw HTML
- `t-if/t-elif/t-else`: Conditional rendering
- `t-foreach`: Loop over collections
- `t-set`: Set variables
- `t-att-*`: Set attributes dynamically

## Performance Monitoring

### Built-in Metrics
- Request timing
- Database query performance
- Memory usage
- Cache hit rates
- Error rates

### Custom Metrics
```python
from erp.logging import get_logger

logger = get_logger(__name__)

# Performance logging
with logger.performance_timer("operation_name"):
    # Your code here
    pass

# Custom metrics
logger.metric("cache_hit_rate", 0.95)
logger.metric("active_users", 150)
```

## Security Features

### Request Tracking
- UUID-based request correlation
- User activity logging
- Database access tracking
- Performance monitoring

### Security Logging
```python
# Security events are automatically logged
logger.security("Failed login attempt", 
                user="unknown", 
                ip="*************")
```

## Error Handling

### Structured Error Logging
```python
try:
    # Risky operation
    pass
except Exception as e:
    logger.error("Operation failed", 
                error=str(e),
                operation="data_import",
                user_id=env.uid)
    raise
```

### Error Recovery
- Automatic database rollback on errors
- Graceful degradation for non-critical failures
- Comprehensive error context logging
