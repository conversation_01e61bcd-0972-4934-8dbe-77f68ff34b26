"""
Odoo-style HTTP route decorator with comprehensive routing support
"""

import functools
import inspect
from typing import Any, Callable, Dict, List, Optional, Union, Type
from enum import Enum
from fastapi import Request, Response, HTTPException
from fastapi.routing import APIRoute
from fastapi.responses import JSONResponse, HTMLResponse, PlainTextResponse
import json

from ..logging import get_logger
from .auth import AuthType, require_auth
from .cors import cors_handler

logger = get_logger(__name__)


class RouteType(str, Enum):
    """Route type enumeration"""
    HTTP = "http"
    JSON = "json"
    # TODO: Add website-related route types when needed
    # WEBSITE = "website"


class HttpMethod(str, Enum):
    """HTTP method enumeration"""
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"
    HEAD = "HEAD"
    OPTIONS = "OPTIONS"


class RouteRegistry:
    """Registry for managing HTTP routes"""
    
    def __init__(self):
        self._routes: Dict[str, Dict] = {}
        self._route_handlers: List[Callable] = []
    
    def register_route(self, path: str, handler: Callable, **kwargs):
        """Register a route handler"""
        route_info = {
            'handler': handler,
            'path': path,
            **kwargs
        }
        self._routes[path] = route_info
        self._route_handlers.append(handler)
        logger.debug(f"Registered route: {kwargs.get('methods', ['GET'])} {path}")
    
    def get_routes(self) -> Dict[str, Dict]:
        """Get all registered routes"""
        return self._routes.copy()
    
    def get_route_handlers(self) -> List[Callable]:
        """Get all route handlers"""
        return self._route_handlers.copy()


# Global route registry
_route_registry = RouteRegistry()


def route(
    path: str,
    *,
    type: RouteType = RouteType.HTTP,
    auth: AuthType = AuthType.USER,
    methods: Optional[List[Union[str, HttpMethod]]] = None,
    cors: Optional[str] = None,
    csrf: bool = True,
    save_session: bool = True,
    database: Optional[str] = None,  # New parameter for database-specific routes
    # TODO: Add website-related parameters when needed
    # website: bool = False,
    # sitemap: bool = True,
    # multilang: bool = True,
    **kwargs
) -> Callable:
    """
    Odoo-style HTTP route decorator
    
    Args:
        path: URL path pattern
        type: Route type (http, json)
        auth: Authentication type (none, public, user, admin)
        methods: HTTP methods (default: ['GET'] for http, ['POST'] for json)
        cors: CORS origin pattern
        csrf: Enable CSRF protection
        save_session: Save session after request
        database: Specific database to register route for (optional)
        **kwargs: Additional route parameters
    
    Returns:
        Decorated function
    
    Example:
        @route('/api/users', type='json', auth='user', methods=['POST'])
        async def get_users(request):
            return {'users': []}
            
        @route('/web/login', type='http', auth='none', methods=['GET', 'POST'])
        async def login_page(request):
            return HTMLResponse('<html>Login</html>')
    """
    
    def decorator(func: Callable) -> Callable:
        # Determine default methods based on route type
        if methods is None:
            default_methods = ['POST'] if type == RouteType.JSON else ['GET']
        else:
            default_methods = [str(m).upper() for m in methods]
        
        # Create route metadata
        route_metadata = {
            'path': path,
            'type': type,
            'auth': auth,
            'methods': default_methods,
            'cors': cors,
            'csrf': csrf,
            'save_session': save_session,
            'database': database,
            'original_func': func,
            **kwargs
        }
        
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            """Route wrapper with authentication and processing"""
            
            # Extract request from args/kwargs
            request = None
            controller_instance = None
            
            if args and isinstance(args[0], Request):
                request = args[0]
            elif 'request' in kwargs:
                request = kwargs['request']
            elif args and hasattr(args[0], '_current_request'):
                # First arg is controller instance
                controller_instance = args[0]
                if len(args) > 1 and isinstance(args[1], Request):
                    request = args[1]
                elif 'request' in kwargs:
                    request = kwargs['request']
            
            if not request:
                raise HTTPException(status_code=500, detail="Request object not found")
            
            try:
                # Apply CORS if specified
                if cors:
                    cors_response = await cors_handler(request, cors)
                    if cors_response:
                        return cors_response
                
                # Apply authentication
                auth_result = await require_auth(request, auth)
                if isinstance(auth_result, Response):
                    return auth_result
                
                # TODO: Add CSRF protection when needed
                # if csrf and type != RouteType.JSON:
                #     csrf_result = await check_csrf(request)
                #     if isinstance(csrf_result, Response):
                #         return csrf_result
                
                # Get current environment
                from ..context import ContextManager
                current_env = ContextManager.get_environment()
                
                # Process form data for POST requests
                if request.method == "POST" and request.headers.get("content-type", "").startswith("application/x-www-form-urlencoded"):
                    try:
                        form_data = await request.form()
                        request._form_data = dict(form_data)
                    except Exception:
                        request._form_data = {}
                else:
                    request._form_data = {}
                
                # Inject request and environment into controller if it's a controller method
                if controller_instance and hasattr(controller_instance, '_set_request_context'):
                    controller_instance._set_request_context(request, current_env)
                
                try:
                    # Call the original function
                    if inspect.iscoroutinefunction(func):
                        result = await func(*args, **kwargs)
                    else:
                        result = func(*args, **kwargs)
                finally:
                    # Clear request context after function execution
                    if controller_instance and hasattr(controller_instance, '_clear_request_context'):
                        controller_instance._clear_request_context()
                
                # Process result based on route type
                if type == RouteType.JSON:
                    # JSON RPC response handling is done in jsonrpc.py
                    return result
                elif type == RouteType.HTTP:
                    # Handle different response types
                    if isinstance(result, (Response, HTMLResponse, JSONResponse, PlainTextResponse)):
                        return result
                    elif isinstance(result, dict):
                        return JSONResponse(content=result)
                    elif isinstance(result, str):
                        return HTMLResponse(content=result)
                    else:
                        return JSONResponse(content={"result": result})
                
                return result
                
            except Exception as e:
                logger.error(f"Error in route {path}: {e}")
                if type == RouteType.JSON:
                    # Return JSON RPC error
                    from .jsonrpc import JsonRpcError
                    return JsonRpcError.internal_error(str(e)).to_response()
                else:
                    raise HTTPException(status_code=500, detail=str(e))
        
        # Store route metadata on the function for lazy discovery
        wrapper._route_metadata = route_metadata
        
        # Store the handler in the module for discovery by DatabaseMemoryRegistry
        # This allows lazy loading to find and register routes when needed
        module = inspect.getmodule(func)
        if module:
            if not hasattr(module, '_route_handlers'):
                module._route_handlers = []
            module._route_handlers.append(wrapper)
            logger.debug(f"Stored route handler for lazy loading: {path}")
        
        return wrapper
    
    return decorator


def _register_route_in_database_registry(path: str, handler: Callable, **kwargs) -> bool:
    """
    Optimized route registration for database-specific registry
    Returns True if successfully scheduled, False otherwise
    """
    try:
        from ..context import ContextManager

        # Get current database from context
        db_name = ContextManager.get_database()
        if not db_name:
            return False

        # Check if we're in an async context
        import asyncio
        try:
            loop = asyncio.get_running_loop()
            # Schedule registration without creating unnecessary tasks
            loop.call_soon_threadsafe(_schedule_route_registration, db_name, path, handler, kwargs)
            return True
        except RuntimeError:
            return False

    except Exception as e:
        logger.debug(f"Failed to schedule route registration: {e}")
        return False


def _schedule_route_registration(db_name: str, path: str, handler: Callable, kwargs: dict):
    """Schedule route registration for later execution"""
    import asyncio

    async def register_async():
        try:
            from ..database.memory import MemoryRegistryManager
            if await MemoryRegistryManager._is_base_module_installed(db_name):
                registry = await MemoryRegistryManager.get_registry(db_name)
                await registry.register_route(path, handler, **kwargs)
        except Exception as e:
            logger.debug(f"Failed to register route for {db_name}: {e}")

    asyncio.create_task(register_async())


def get_route_registry() -> RouteRegistry:
    """Get the global route registry"""
    return _route_registry


async def get_database_route_registry(db_name: str):
    """Get route registry for a specific database"""
    from ..database.memory import MemoryRegistryManager

    # Only get registry if base module is installed to avoid issues during initialization
    if await MemoryRegistryManager._is_base_module_installed(db_name):
        registry = await MemoryRegistryManager.get_registry(db_name)
        return registry
    else:
        logger.debug(f"Cannot get route registry for {db_name} - base module not installed yet")
        return None


async def get_all_database_routes() -> Dict[str, Dict[str, Dict]]:
    """Get routes from all database registries"""
    from ..database.memory import MemoryRegistryManager
    
    all_registries = await MemoryRegistryManager.get_all_registries()
    database_routes = {}
    
    for db_name, registry in all_registries.items():
        routes = await registry.get_routes()
        if routes:
            database_routes[db_name] = routes
    
    return database_routes


async def register_route_for_database(db_name: str, path: str, handler: Callable, **kwargs):
    """
    Optimized route registration for a specific database

    Args:
        db_name: Database name
        path: Route path
        handler: Route handler function
        **kwargs: Route metadata
    """
    from ..database.memory import MemoryRegistryManager

    try:
        if await MemoryRegistryManager._is_base_module_installed(db_name):
            registry = await MemoryRegistryManager.get_registry(db_name)
            await registry.register_route(path, handler, **kwargs)
            logger.info(f"Registered route for {db_name}: {kwargs.get('methods', ['GET'])} {path}")
        else:
            logger.debug(f"Skipping route registration for {db_name} - base module not installed")
    except Exception as e:
        logger.error(f"Failed to register route for {db_name}: {e}")


# DatabaseRouteManager removed - functionality moved to MemoryRegistryManager


class RouteIntegration:
    """Integration layer for registering routes with FastAPI"""

    @staticmethod
    def register_routes_with_fastapi(app, route_registry: RouteRegistry):
        """
        Register all routes from registry with FastAPI app

        Args:
            app: FastAPI application instance
            route_registry: RouteRegistry containing routes to register
        """
        RouteIntegration._register_routes_from_registry(app, route_registry.get_routes(), "global")

    @staticmethod
    async def register_database_routes_with_fastapi(app):
        """
        Register all database-specific routes with FastAPI app

        Args:
            app: FastAPI application instance
        """
        try:
            database_routes = await get_all_database_routes()
            
            for db_name, routes in database_routes.items():
                RouteIntegration._register_routes_from_registry(app, routes, f"database_{db_name}")
                
            total_routes = sum(len(routes) for routes in database_routes.values())
            logger.info(f"Registered {total_routes} database-specific routes from {len(database_routes)} databases")
            
        except Exception as e:
            logger.error(f"Failed to register database routes: {e}")

    @staticmethod
    def _register_routes_from_registry(app, routes: Dict[str, Dict], source: str):
        """
        Register routes from a routes dictionary with FastAPI app

        Args:
            app: FastAPI application instance
            routes: Dictionary of routes to register
            source: Source identifier for logging
        """
        from fastapi import APIRouter
        from .jsonrpc import get_jsonrpc_handler
        import inspect

        # Create router for HTTP routes
        http_router = APIRouter(tags=[f"http_routes_{source}"])

        # Create router for JSON RPC routes
        jsonrpc_router = APIRouter(prefix="/jsonrpc", tags=[f"jsonrpc_{source}"])

        jsonrpc_handler = get_jsonrpc_handler()

        for path, route_info in routes.items():
            handler = route_info['handler']
            route_type = route_info.get('type', RouteType.HTTP)
            methods = route_info.get('methods', ['GET'])

            if route_type == RouteType.JSON:
                # Register as JSON RPC method
                method_name = route_info.get('jsonrpc_method', handler.__name__)
                jsonrpc_handler.register_method(method_name, handler)

                # Add JSON RPC endpoint if not already added
                if not hasattr(jsonrpc_router, '_jsonrpc_endpoint_added'):
                    @jsonrpc_router.post("")
                    @jsonrpc_router.post("/")
                    async def jsonrpc_endpoint(request):
                        return await jsonrpc_handler.handle_request(request)

                    jsonrpc_router._jsonrpc_endpoint_added = True

            else:
                # Optimized handler processing
                actual_handler = RouteIntegration._get_optimized_handler(handler, path)

                # Create FastAPI-compatible wrapper
                fastapi_handler = RouteIntegration._create_fastapi_wrapper(actual_handler)

                # Register as HTTP route
                logger.debug(f"Registering HTTP route: {methods} {path}")

                # Register the route with all specified methods at once
                http_router.add_api_route(
                    path,
                    fastapi_handler,
                    methods=[method.upper() for method in methods],
                    tags=[f"{source}_http"]
                )

        # Include routers in the main app
        app.include_router(http_router)
        app.include_router(jsonrpc_router)

        logger.debug(f"Registered {len(routes)} routes from {source}")

    @staticmethod
    def _get_optimized_handler(handler, path: str):
        """Get optimized handler for route registration"""
        # Check if handler needs controller instantiation
        if hasattr(handler, '_route_metadata'):
            original_func = handler._route_metadata.get('original_func')
            if original_func and hasattr(original_func, '__qualname__'):
                qualname_parts = original_func.__qualname__.split('.')
                if len(qualname_parts) > 1:
                    try:
                        return RouteIntegration._create_controller_handler(original_func, qualname_parts, path)
                    except Exception as e:
                        logger.debug(f"Could not create controller handler for {path}: {e}")

        return handler

    @staticmethod
    def _create_controller_handler(original_func, qualname_parts, path: str):
        """Create optimized controller handler"""
        module = inspect.getmodule(original_func)
        if not module:
            return None

        class_name = qualname_parts[-2]
        controller_class = getattr(module, class_name, None)

        if controller_class and hasattr(controller_class, '__bases__'):
            from .controller import Controller
            if issubclass(controller_class, Controller):
                controller_instance = controller_class()
                method_name = qualname_parts[-1]
                bound_method = getattr(controller_instance, method_name)

                async def controller_wrapper(request):
                    if inspect.iscoroutinefunction(bound_method):
                        return await bound_method(request)
                    else:
                        return bound_method(request)

                return controller_wrapper

        return None

    @staticmethod
    def _create_fastapi_wrapper(handler):
        """Create FastAPI-compatible wrapper"""
        async def fastapi_endpoint(request: Request):
            return await handler(request)

        fastapi_endpoint.__name__ = getattr(handler, '__name__', 'unknown_handler')
        fastapi_endpoint.__doc__ = getattr(handler, '__doc__', None)

        return fastapi_endpoint


def setup_http_routes(app):
    """
    Setup HTTP routes with FastAPI application
    Only registers system/global routes, NO database routes during server startup

    Args:
        app: FastAPI application instance
    """
    # Register ONLY global/system routes during server startup
    RouteIntegration.register_routes_with_fastapi(app, _route_registry)

    # Database-specific routes will be registered lazily when databases are accessed
    # NO database route registration during server startup


# Removed redundant setup_http_routes_async - use setup_http_routes instead
