"""
Base Module Registrar
Handles base module registration in ir.module.module table.
Separated from installer for clear separation of concerns.
"""
import os
from typing import TYPE_CHECKING
from erp.logging import get_logger

if TYPE_CHECKING:
    from erp.database.manager import DatabaseManager


class BaseModuleRegistrar:
    """
    Manages base module registration in the ir.module.module table.
    Handles creating the base module record without legacy dependencies.
    """

    def __init__(self):
        self.logger = get_logger(__name__)

    async def register_base_module(self, db_manager: 'DatabaseManager') -> bool:
        """Register base module in ir.module.module table using direct database operations"""
        try:
            from datetime import datetime

            # Get base addon path using modern path resolution
            current_dir = os.path.dirname(__file__)
            base_addon_path = os.path.abspath(current_dir)

            self.logger.debug(f"Registering base module from path: {base_addon_path}")

            # Read manifest directly without legacy dependencies
            manifest_data = await self._read_base_manifest(base_addon_path)
            
            # Register using direct database operations
            success = await self._insert_base_module_record(db_manager, manifest_data)

            if not success:
                self.logger.error("Failed to register base module in ir.module.module table")
                return False

            self.logger.debug("✓ Base module record created/updated")
            return True
        except Exception as e:
            self.logger.error(f"Failed to register base module: {e}")
            return False

    async def _read_base_manifest(self, base_addon_path: str) -> dict:
        """Read base module manifest without legacy dependencies"""
        try:
            manifest_path = os.path.join(base_addon_path, '__manifest__.py')
            
            if not os.path.exists(manifest_path):
                self.logger.warning(f"Manifest not found at {manifest_path}, using defaults")
                return self._get_default_base_manifest()

            # Read manifest file safely
            manifest_globals = {}
            with open(manifest_path, 'r', encoding='utf-8') as f:
                manifest_code = f.read()
                exec(manifest_code, manifest_globals)

            # Extract manifest data
            manifest = manifest_globals.get('__manifest__', {})
            
            if not manifest:
                self.logger.warning("No __manifest__ found in manifest file, using defaults")
                return self._get_default_base_manifest()

            return manifest

        except Exception as e:
            self.logger.warning(f"Failed to read base manifest: {e}, using defaults")
            return self._get_default_base_manifest()

    def _get_default_base_manifest(self) -> dict:
        """Get default manifest data for base module"""
        return {
            'name': 'Base',
            'summary': 'Base module providing core functionality',
            'description': 'Core base module that provides fundamental models and functionality for the ERP system',
            'author': 'ERP System',
            'version': '1.0.0',
            'category': 'Hidden',
            'installable': True,
            'auto_install': True,
            'application': False,
            'sequence': 1
        }

    async def _insert_base_module_record(self, db_manager: 'DatabaseManager', manifest: dict) -> bool:
        """Insert base module record directly into database"""
        try:
            from datetime import datetime

            current_time = datetime.now()

            # First check if table exists
            table_exists = await db_manager.fetchval(
                """SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = 'ir_module_module'
                )"""
            )
            self.logger.debug(f"ir_module_module table exists: {table_exists}")

            if not table_exists:
                self.logger.error("ir_module_module table does not exist - cannot register base module")
                return False

            # Check if base module already exists
            exists = await db_manager.fetchval(
                "SELECT EXISTS(SELECT 1 FROM ir_module_module WHERE name = $1)",
                'base'
            )

            if exists:
                # Update existing record
                await db_manager.execute(
                    """UPDATE ir_module_module SET
                       display_name = $1,
                       summary = $2,
                       description = $3,
                       author = $4,
                       version = $5,
                       category = $6,
                       installable = $7,
                       auto_install = $8,
                       application = $9,
                       sequence = $10,
                       state = $11,
                       update_at = $12
                       WHERE name = $13""",
                    manifest.get('name', 'Base'),
                    manifest.get('summary', ''),
                    manifest.get('description', ''),
                    manifest.get('author', 'ERP System'),
                    manifest.get('version', '1.0.0'),
                    manifest.get('category', 'Hidden'),
                    manifest.get('installable', True),
                    manifest.get('auto_install', True),
                    manifest.get('application', False),
                    manifest.get('sequence', 1),
                    'installed',
                    current_time,
                    'base'
                )
                self.logger.debug("✓ Base module record updated")
            else:
                # Insert new record
                await db_manager.execute(
                    """INSERT INTO ir_module_module (
                       name, display_name, summary, description, author, version,
                       category, installable, auto_install, application, sequence,
                       state, create_at, update_at, action_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)""",
                    'base',
                    manifest.get('name', 'Base'),
                    manifest.get('summary', ''),
                    manifest.get('description', ''),
                    manifest.get('author', 'ERP System'),
                    manifest.get('version', '1.0.0'),
                    manifest.get('category', 'Hidden'),
                    manifest.get('installable', True),
                    manifest.get('auto_install', True),
                    manifest.get('application', False),
                    manifest.get('sequence', 1),
                    'installed',
                    current_time,
                    current_time,
                    current_time
                )
                self.logger.debug("✓ Base module record created")

            return True

        except Exception as e:
            self.logger.error(f"Failed to insert/update base module record: {e}")
            return False

    async def validate_base_module_registration(self, db_manager: 'DatabaseManager') -> bool:
        """Validate that base module is properly registered"""
        try:
            # Check that base module exists and is marked as installed
            result = await db_manager.fetchrow(
                "SELECT name, state, installable FROM ir_module_module WHERE name = $1",
                'base'
            )

            if not result:
                self.logger.error("Base module not found in ir_module_module table")
                return False

            if result['state'] != 'installed':
                self.logger.error(f"Base module state is '{result['state']}', expected 'installed'")
                return False

            if not result['installable']:
                self.logger.error("Base module is marked as not installable")
                return False

            self.logger.debug("✓ Base module registration validation completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to validate base module registration: {e}")
            return False
