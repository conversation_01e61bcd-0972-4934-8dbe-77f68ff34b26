"""
Domain filtering utilities for ERP models
Provides reusable domain filtering logic for database queries and record filtering
"""
from typing import List, Tuple, Any, Dict, Union


class DomainFilter:
    """
    Utility class for handling domain filtering operations
    Supports various operators for filtering records and building database queries
    """
    
    # Supported operators
    OPERATORS = {
        '=': 'equals',
        '!=': 'not_equals', 
        'like': 'like',
        'ilike': 'ilike',
        'in': 'in_list',
        'not in': 'not_in_list',
        '>': 'greater_than',
        '<': 'less_than',
        '>=': 'greater_equal',
        '<=': 'less_equal',
        '=?': 'equals_or_null',
        '=like': 'equals_like',
        '=ilike': 'equals_ilike'
    }
    
    @classmethod
    def validate_domain(cls, domain: List) -> bool:
        """
        Validate domain format
        
        Args:
            domain: List of domain conditions
            
        Returns:
            True if domain is valid
            
        Raises:
            ValueError: If domain format is invalid
        """
        if not isinstance(domain, list):
            raise ValueError("Domain must be a list")
        
        for condition in domain:
            if not isinstance(condition, (list, tuple)) or len(condition) != 3:
                raise ValueError(f"Invalid domain condition: {condition}. Must be [field, operator, value]")
            
            field, operator, value = condition
            if not isinstance(field, str):
                raise ValueError(f"Field name must be string, got {type(field)}")
            
            if operator not in cls.OPERATORS:
                raise ValueError(f"Unsupported operator: {operator}")
        
        return True
    
    @classmethod
    def build_sql_where_clause(cls, domain: List, param_offset: int = 0) -> Tuple[str, List[Any]]:
        """
        Build SQL WHERE clause from domain conditions
        
        Args:
            domain: List of domain conditions
            param_offset: Starting parameter number for SQL placeholders
            
        Returns:
            Tuple of (where_clause, parameters)
        """
        if not domain:
            return "", []
        
        cls.validate_domain(domain)
        
        where_conditions = []
        params = []
        param_count = param_offset
        
        for condition in domain:
            field, operator, value = condition
            
            if operator == '=':
                where_conditions.append(f"{field} = ${param_count + 1}")
                params.append(value)
                param_count += 1
                
            elif operator == '!=':
                where_conditions.append(f"{field} != ${param_count + 1}")
                params.append(value)
                param_count += 1
                
            elif operator == 'like':
                where_conditions.append(f"{field} LIKE ${param_count + 1}")
                params.append(value)
                param_count += 1
                
            elif operator == 'ilike':
                where_conditions.append(f"{field} ILIKE ${param_count + 1}")
                params.append(value)
                param_count += 1
                
            elif operator == 'in':
                if not isinstance(value, (list, tuple)):
                    raise ValueError(f"'in' operator requires list/tuple value, got {type(value)}")
                if not value:
                    # Empty list - condition will never match
                    where_conditions.append("FALSE")
                else:
                    placeholders = ','.join([f"${param_count + i + 1}" for i in range(len(value))])
                    where_conditions.append(f"{field} IN ({placeholders})")
                    params.extend(value)
                    param_count += len(value)
                    
            elif operator == 'not in':
                if not isinstance(value, (list, tuple)):
                    raise ValueError(f"'not in' operator requires list/tuple value, got {type(value)}")
                if not value:
                    # Empty list - condition always matches
                    where_conditions.append("TRUE")
                else:
                    placeholders = ','.join([f"${param_count + i + 1}" for i in range(len(value))])
                    where_conditions.append(f"{field} NOT IN ({placeholders})")
                    params.extend(value)
                    param_count += len(value)
                    
            elif operator == '>':
                where_conditions.append(f"{field} > ${param_count + 1}")
                params.append(value)
                param_count += 1
                
            elif operator == '<':
                where_conditions.append(f"{field} < ${param_count + 1}")
                params.append(value)
                param_count += 1
                
            elif operator == '>=':
                where_conditions.append(f"{field} >= ${param_count + 1}")
                params.append(value)
                param_count += 1
                
            elif operator == '<=':
                where_conditions.append(f"{field} <= ${param_count + 1}")
                params.append(value)
                param_count += 1
                
            elif operator == '=?':
                # Equals or null - special case
                where_conditions.append(f"({field} = ${param_count + 1} OR {field} IS NULL)")
                params.append(value)
                param_count += 1
                
            elif operator == '=like':
                # Equals or like
                where_conditions.append(f"({field} = ${param_count + 1} OR {field} LIKE ${param_count + 2})")
                params.extend([value, value])
                param_count += 2
                
            elif operator == '=ilike':
                # Equals or ilike
                where_conditions.append(f"({field} = ${param_count + 1} OR {field} ILIKE ${param_count + 2})")
                params.extend([value, value])
                param_count += 2
        
        where_clause = " AND ".join(where_conditions) if where_conditions else ""
        return where_clause, params
    
    @classmethod
    def filter_records(cls, records: List[Any], domain: List) -> List[Any]:
        """
        Filter a list of records based on domain conditions
        
        Args:
            records: List of record objects
            domain: List of domain conditions
            
        Returns:
            Filtered list of records
        """
        if not domain:
            return records
        
        cls.validate_domain(domain)
        
        filtered_records = []
        
        for record in records:
            if cls._record_matches_domain(record, domain):
                filtered_records.append(record)
        
        return filtered_records
    
    @classmethod
    def _record_matches_domain(cls, record: Any, domain: List) -> bool:
        """
        Check if a record matches all domain conditions
        
        Args:
            record: Record object to check
            domain: List of domain conditions
            
        Returns:
            True if record matches all conditions
        """
        for condition in domain:
            field, operator, value = condition
            
            # Get field value from record
            if hasattr(record, field):
                record_value = getattr(record, field)
            else:
                # Field doesn't exist - condition fails
                return False
            
            # Check condition
            if not cls._check_condition(record_value, operator, value):
                return False
        
        return True
    
    @classmethod
    def _check_condition(cls, record_value: Any, operator: str, domain_value: Any) -> bool:
        """
        Check if a single condition matches
        
        Args:
            record_value: Value from the record
            operator: Comparison operator
            domain_value: Value from domain condition
            
        Returns:
            True if condition matches
        """
        if operator == '=':
            return record_value == domain_value
            
        elif operator == '!=':
            return record_value != domain_value
            
        elif operator == 'like':
            if record_value is None:
                return False
            return str(domain_value).replace('%', '.*') in str(record_value)
            
        elif operator == 'ilike':
            if record_value is None:
                return False
            return str(domain_value).replace('%', '.*').lower() in str(record_value).lower()
            
        elif operator == 'in':
            return record_value in domain_value
            
        elif operator == 'not in':
            return record_value not in domain_value
            
        elif operator == '>':
            return record_value > domain_value
            
        elif operator == '<':
            return record_value < domain_value
            
        elif operator == '>=':
            return record_value >= domain_value
            
        elif operator == '<=':
            return record_value <= domain_value
            
        elif operator == '=?':
            return record_value == domain_value or record_value is None
            
        elif operator == '=like':
            if record_value == domain_value:
                return True
            if record_value is None:
                return False
            return str(domain_value).replace('%', '.*') in str(record_value)
            
        elif operator == '=ilike':
            if record_value == domain_value:
                return True
            if record_value is None:
                return False
            return str(domain_value).replace('%', '.*').lower() in str(record_value).lower()
        
        return False
    
    @classmethod
    def combine_domains(cls, *domains: List) -> List:
        """
        Combine multiple domains with AND logic
        
        Args:
            domains: Multiple domain lists to combine
            
        Returns:
            Combined domain list
        """
        combined = []
        for domain in domains:
            if domain:
                combined.extend(domain)
        return combined
    
    @classmethod
    def domain_to_string(cls, domain: List) -> str:
        """
        Convert domain to human-readable string representation
        
        Args:
            domain: Domain list
            
        Returns:
            String representation of domain
        """
        if not domain:
            return "No filters"
        
        conditions = []
        for condition in domain:
            field, operator, value = condition
            conditions.append(f"{field} {operator} {repr(value)}")
        
        return " AND ".join(conditions)