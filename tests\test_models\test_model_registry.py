"""
Test suite for ModelRegistry functionality

This module tests:
- ModelRegistry creation and initialization
- Model registration and discovery
- Addon-specific model registry operations
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, List

from erp.models.base import BaseModel
from erp.models.registry import ModelRegistry, create_addon_model_registry


class TestModelRegistry:
    """Test ModelRegistry functionality"""

    def test_model_registry_basic_functionality(self):
        """Test basic ModelRegistry functionality"""
        registry = create_addon_model_registry("test_addon")

        assert isinstance(registry, ModelRegistry)
        assert registry.addon_name == "test_addon"

    def test_model_registry_initialization(self):
        """Test ModelRegistry initialization"""
        registry = ModelRegistry("test_addon")

        assert registry.addon_name == "test_addon"
        assert registry._models == {}
        assert registry._discovered is False

    def test_create_addon_model_registry(self):
        """Test factory function for creating model registry"""
        registry = create_addon_model_registry("test_addon")

        assert isinstance(registry, ModelRegistry)
        assert registry.addon_name == "test_addon"

    @patch('erp.models.registry.importlib.import_module')
    @patch('erp.models.registry.inspect.getmembers')
    def test_model_discovery(self, mock_getmembers, mock_import):
        """Test model discovery from addon modules"""
        registry = ModelRegistry("test_addon")

        # Mock model class
        mock_model = MagicMock()
        mock_model.__name__ = "TestModel"
        mock_model._name = "test.model"

        # Mock module discovery
        mock_getmembers.return_value = [("TestModel", mock_model)]
        mock_import.return_value = MagicMock()

        with patch('erp.models.registry.Path.exists', return_value=True):
            with patch('erp.models.registry.os.listdir', return_value=["test_model.py"]):
                registry.discover_models()

        assert registry._discovered is True
        assert "test.model" in registry._models

    def test_model_registry_all_models(self):
        """Test getting all models from registry"""
        registry = ModelRegistry("test_addon")

        # Add mock models
        mock_model1 = MagicMock()
        mock_model2 = MagicMock()
        registry._models = {
            "model1": mock_model1,
            "model2": mock_model2
        }

        all_models = registry.all()
        assert len(all_models) == 2
        assert "model1" in all_models
        assert "model2" in all_models

    def test_model_registry_get_model_fields(self):
        """Test getting model fields"""
        registry = ModelRegistry("test_addon")

        # Mock model with fields
        mock_model = MagicMock()
        mock_field1 = MagicMock()
        mock_field1.name = "field1"
        mock_field1.string = "Field 1"
        mock_field2 = MagicMock()
        mock_field2.name = "field2"
        mock_field2.string = "Field 2"

        mock_model._fields = {
            "field1": mock_field1,
            "field2": mock_field2
        }

        registry._models = {"test.model": mock_model}

        fields = registry.get_model_fields("test.model")
        assert len(fields) == 2
        assert "field1" in fields
        assert "field2" in fields

    def test_model_registry_clear(self):
        """Test clearing model registry"""
        registry = ModelRegistry("test_addon")
        registry._models = {"test.model": MagicMock()}
        registry._discovered = True

        registry.clear()

        assert registry._models == {}
        assert registry._discovered is False
