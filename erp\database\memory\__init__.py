"""
In-memory registry system for databases
"""
from .database_memory import DatabaseMemoryRegistry
from .registry_manager import MemoryRegistryManager
from .filter_processor import DatabaseFilterProcessor
from .cache_manager import CacheManager, QueryCache
from .addon_manager import AddonManager
from .model_metadata_manager import ModelMetadataManager
from .route_manager import RouteManager

__all__ = [
    'DatabaseMemoryRegistry',
    'MemoryRegistryManager',
    'DatabaseFilterProcessor',
    'CacheManager',
    'QueryCache',
    'AddonManager',
    'ModelMetadataManager',
    'RouteManager'
]

# Convenience function for external access
def get_memory_registry_manager() -> MemoryRegistryManager:
    """
    Convenience function to get the MemoryRegistryManager class
    Provides easy access to the singleton registry manager
    """
    return MemoryRegistryManager
