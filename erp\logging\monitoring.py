"""
Logging Monitoring - Performance and system monitoring utilities
"""
import logging
import time
import psutil
import threading
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
import asyncio
from .utils import log_structured


@dataclass
class PerformanceMetrics:
    """Performance metrics data structure"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_sent_mb: float
    network_recv_mb: float
    active_connections: int
    request_count: int
    error_count: int
    avg_response_time: float


class PerformanceMonitor:
    """System performance monitoring with logging"""
    
    def __init__(self, logger: Optional[logging.Logger] = None, 
                 interval: float = 60.0, 
                 alert_thresholds: Optional[Dict[str, float]] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.interval = interval
        self.alert_thresholds = alert_thresholds or {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_usage_percent': 90.0,
            'avg_response_time': 5.0
        }
        
        self.monitoring = False
        self.monitor_thread = None
        self.metrics_history: List[PerformanceMetrics] = []
        self.max_history = 1440  # 24 hours at 1-minute intervals
        
        # Request tracking
        self.request_count = 0
        self.error_count = 0
        self.response_times: List[float] = []
        self.active_connections = 0
        
        # Previous values for delta calculations
        self.prev_disk_io = None
        self.prev_network_io = None
        
    def start_monitoring(self):
        """Start performance monitoring"""
        if self.monitoring:
            return
            
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info("Performance monitoring started", extra={
            'operation': 'monitoring_start',
            'interval': self.interval
        })
        
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
            
        self.logger.info("Performance monitoring stopped", extra={
            'operation': 'monitoring_stop'
        })
        
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                metrics = self._collect_metrics()
                self._log_metrics(metrics)
                self._check_alerts(metrics)
                self._store_metrics(metrics)
                
            except Exception as e:
                self.logger.error(f"Error in performance monitoring: {e}", extra={
                    'operation': 'monitoring_error',
                    'error_type': type(e).__name__
                })
                
            time.sleep(self.interval)
            
    def _collect_metrics(self) -> PerformanceMetrics:
        """Collect current system metrics"""
        # CPU and Memory
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        
        # Disk I/O
        disk_io = psutil.disk_io_counters()
        disk_read_mb = 0
        disk_write_mb = 0
        
        if disk_io and self.prev_disk_io:
            disk_read_mb = (disk_io.read_bytes - self.prev_disk_io.read_bytes) / 1024 / 1024
            disk_write_mb = (disk_io.write_bytes - self.prev_disk_io.write_bytes) / 1024 / 1024
            
        self.prev_disk_io = disk_io
        
        # Network I/O
        network_io = psutil.net_io_counters()
        network_sent_mb = 0
        network_recv_mb = 0
        
        if network_io and self.prev_network_io:
            network_sent_mb = (network_io.bytes_sent - self.prev_network_io.bytes_sent) / 1024 / 1024
            network_recv_mb = (network_io.bytes_recv - self.prev_network_io.bytes_recv) / 1024 / 1024
            
        self.prev_network_io = network_io
        
        # Calculate average response time
        avg_response_time = 0.0
        if self.response_times:
            avg_response_time = sum(self.response_times) / len(self.response_times)
            self.response_times.clear()  # Reset for next interval
            
        return PerformanceMetrics(
            timestamp=datetime.now(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_mb=memory.used / 1024 / 1024,
            disk_io_read_mb=disk_read_mb,
            disk_io_write_mb=disk_write_mb,
            network_sent_mb=network_sent_mb,
            network_recv_mb=network_recv_mb,
            active_connections=self.active_connections,
            request_count=self.request_count,
            error_count=self.error_count,
            avg_response_time=avg_response_time
        )
        
    def _log_metrics(self, metrics: PerformanceMetrics):
        """Log performance metrics"""
        log_structured(
            self.logger, logging.INFO,
            "Performance metrics collected",
            operation='performance_metrics',
            cpu_percent=metrics.cpu_percent,
            memory_percent=metrics.memory_percent,
            memory_used_mb=metrics.memory_used_mb,
            disk_read_mb=metrics.disk_io_read_mb,
            disk_write_mb=metrics.disk_io_write_mb,
            network_sent_mb=metrics.network_sent_mb,
            network_recv_mb=metrics.network_recv_mb,
            active_connections=metrics.active_connections,
            request_count=metrics.request_count,
            error_count=metrics.error_count,
            avg_response_time=metrics.avg_response_time
        )
        
        # Reset counters
        self.request_count = 0
        self.error_count = 0
        
    def _check_alerts(self, metrics: PerformanceMetrics):
        """Check for performance alerts"""
        alerts = []
        
        if metrics.cpu_percent > self.alert_thresholds['cpu_percent']:
            alerts.append(f"High CPU usage: {metrics.cpu_percent:.1f}%")
            
        if metrics.memory_percent > self.alert_thresholds['memory_percent']:
            alerts.append(f"High memory usage: {metrics.memory_percent:.1f}%")
            
        if metrics.avg_response_time > self.alert_thresholds['avg_response_time']:
            alerts.append(f"High response time: {metrics.avg_response_time:.2f}s")
            
        # Check disk usage
        for partition in psutil.disk_partitions():
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                usage_percent = (usage.used / usage.total) * 100
                if usage_percent > self.alert_thresholds['disk_usage_percent']:
                    alerts.append(f"High disk usage on {partition.mountpoint}: {usage_percent:.1f}%")
            except Exception:
                pass  # Skip inaccessible partitions
                
        # Log alerts
        for alert in alerts:
            log_structured(
                self.logger, logging.WARNING,
                f"Performance alert: {alert}",
                operation='performance_alert',
                alert_type='threshold_exceeded',
                alert_message=alert
            )
            
    def _store_metrics(self, metrics: PerformanceMetrics):
        """Store metrics in history"""
        self.metrics_history.append(metrics)
        
        # Keep only recent history
        if len(self.metrics_history) > self.max_history:
            self.metrics_history = self.metrics_history[-self.max_history:]
            
    def record_request(self, response_time: float, is_error: bool = False):
        """Record a request for metrics"""
        self.request_count += 1
        self.response_times.append(response_time)
        
        if is_error:
            self.error_count += 1
            
    def increment_connections(self):
        """Increment active connection count"""
        self.active_connections += 1
        
    def decrement_connections(self):
        """Decrement active connection count"""
        self.active_connections = max(0, self.active_connections - 1)
        
    def get_recent_metrics(self, minutes: int = 60) -> List[PerformanceMetrics]:
        """Get metrics from the last N minutes"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [m for m in self.metrics_history if m.timestamp >= cutoff_time]
        
    def get_summary_stats(self, minutes: int = 60) -> Dict[str, Any]:
        """Get summary statistics for the last N minutes"""
        recent_metrics = self.get_recent_metrics(minutes)
        
        if not recent_metrics:
            return {}
            
        return {
            'avg_cpu_percent': sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics),
            'max_cpu_percent': max(m.cpu_percent for m in recent_metrics),
            'avg_memory_percent': sum(m.memory_percent for m in recent_metrics) / len(recent_metrics),
            'max_memory_percent': max(m.memory_percent for m in recent_metrics),
            'total_requests': sum(m.request_count for m in recent_metrics),
            'total_errors': sum(m.error_count for m in recent_metrics),
            'avg_response_time': sum(m.avg_response_time for m in recent_metrics) / len(recent_metrics),
            'max_response_time': max(m.avg_response_time for m in recent_metrics),
            'period_minutes': minutes,
            'sample_count': len(recent_metrics)
        }


# Global performance monitor instance
_performance_monitor: Optional[PerformanceMonitor] = None


def get_performance_monitor() -> PerformanceMonitor:
    """Get the global performance monitor instance"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor


def start_performance_monitoring(logger: Optional[logging.Logger] = None,
                                interval: float = 60.0,
                                alert_thresholds: Optional[Dict[str, float]] = None):
    """Start global performance monitoring"""
    monitor = get_performance_monitor()
    if logger:
        monitor.logger = logger
    if interval != 60.0:
        monitor.interval = interval
    if alert_thresholds:
        monitor.alert_thresholds.update(alert_thresholds)
        
    monitor.start_monitoring()


def stop_performance_monitoring():
    """Stop global performance monitoring"""
    monitor = get_performance_monitor()
    monitor.stop_monitoring()


def get_memory_usage() -> Dict[str, float]:
    """Get current memory usage information"""
    try:
        memory = psutil.virtual_memory()
        process = psutil.Process()
        process_memory = process.memory_info()
        
        return {
            'system_total_mb': memory.total / 1024 / 1024,
            'system_used_mb': memory.used / 1024 / 1024,
            'system_available_mb': memory.available / 1024 / 1024,
            'system_percent': memory.percent,
            'process_rss_mb': process_memory.rss / 1024 / 1024,
            'process_vms_mb': process_memory.vms / 1024 / 1024,
        }
    except Exception as e:
        return {'error': str(e)}


def log_memory_freed(logger: logging.Logger, before_memory: Dict[str, float], after_memory: Dict[str, float]):
    """Log memory freed during shutdown"""
    try:
        if 'error' in before_memory or 'error' in after_memory:
            logger.warning("Could not calculate memory freed due to errors in memory collection")
            return
            
        # Calculate memory freed
        process_memory_freed = before_memory['process_rss_mb'] - after_memory['process_rss_mb']
        system_memory_freed = before_memory['system_used_mb'] - after_memory['system_used_mb']
        
        # Log the memory information
        logger.info(
            f"💾 Memory freed during shutdown: "
            f"Process RSS: {process_memory_freed:.2f} MB, "
            f"System: {system_memory_freed:.2f} MB",
            extra={
                'operation': 'memory_freed',
                'process_memory_freed_mb': process_memory_freed,
                'system_memory_freed_mb': system_memory_freed,
                'before_process_rss_mb': before_memory['process_rss_mb'],
                'after_process_rss_mb': after_memory['process_rss_mb'],
                'before_system_used_mb': before_memory['system_used_mb'],
                'after_system_used_mb': after_memory['system_used_mb'],
                'before_system_percent': before_memory['system_percent'],
                'after_system_percent': after_memory['system_percent']
            }
        )
        
        # Additional detailed logging if significant memory was freed
        if process_memory_freed > 10:  # More than 10MB freed
            logger.info(
                f"📊 Significant memory freed - Process details: "
                f"RSS: {before_memory['process_rss_mb']:.2f} MB → {after_memory['process_rss_mb']:.2f} MB, "
                f"VMS: {before_memory['process_vms_mb']:.2f} MB → {after_memory['process_vms_mb']:.2f} MB"
            )
            
    except Exception as e:
        logger.error(f"Error calculating memory freed: {e}")
