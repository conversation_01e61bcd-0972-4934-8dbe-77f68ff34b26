"""
Environment system for ERP
Provides Odoo-like Environment with cr, uid, context properties
"""
from typing import Dict, Any, Optional, Union, TYPE_CHECKING
from contextlib import asynccontextmanager
import asyncio
import uuid
from .context import ContextManager
from .logging import get_logger

if TYPE_CHECKING:
    from .database.connection import DatabaseManager
    from .database.memory import DatabaseMemoryRegistry


class DatabaseCursor:
    """
    Database cursor wrapper that provides Odoo-like interface
    """
    
    def __init__(self, db_manager: 'DatabaseManager', db_name: str):
        self._db_manager = db_manager
        self.db_name = db_name
        self._connection = None
        self._in_transaction = False
    
    @asynccontextmanager
    async def _get_connection(self):
        """Get database connection from pool"""
        async with self._db_manager.acquire_connection() as conn:
            self._connection = conn
            try:
                yield conn
            finally:
                self._connection = None
    
    async def execute(self, query: str, *args) -> str:
        """Execute a query"""
        if self._connection:
            return await self._connection.execute(query, *args)
        else:
            return await self._db_manager.execute(query, *args)
    
    async def fetch(self, query: str, *args) -> list:
        """Fetch multiple rows"""
        if self._connection:
            return await self._connection.fetch(query, *args)
        else:
            return await self._db_manager.fetch(query, *args)
    
    async def fetchrow(self, query: str, *args) -> Optional[dict]:
        """Fetch single row"""
        if self._connection:
            result = await self._connection.fetchrow(query, *args)
        else:
            result = await self._db_manager.fetchrow(query, *args)
        
        return dict(result) if result else None
    
    async def fetchval(self, query: str, *args) -> Any:
        """Fetch single value"""
        if self._connection:
            return await self._connection.fetchval(query, *args)
        else:
            return await self._db_manager.fetchval(query, *args)
    
    async def commit(self):
        """Commit transaction"""
        if self._connection and self._in_transaction:
            await self._connection.execute("COMMIT")
            self._in_transaction = False
    
    async def rollback(self):
        """Rollback transaction"""
        if self._connection and self._in_transaction:
            await self._connection.execute("ROLLBACK")
            self._in_transaction = False
    
    async def begin(self):
        """Begin transaction"""
        if self._connection and not self._in_transaction:
            await self._connection.execute("BEGIN")
            self._in_transaction = True
    
    async def savepoint(self, name: str):
        """Create a savepoint"""
        if self._connection and self._in_transaction:
            await self._connection.execute(f"SAVEPOINT {name}")
    
    async def release_savepoint(self, name: str):
        """Release a savepoint"""
        if self._connection and self._in_transaction:
            await self._connection.execute(f"RELEASE SAVEPOINT {name}")
    
    async def rollback_to_savepoint(self, name: str):
        """Rollback to a savepoint"""
        if self._connection and self._in_transaction:
            await self._connection.execute(f"ROLLBACK TO SAVEPOINT {name}")
    
    @property
    def in_transaction(self) -> bool:
        """Check if cursor is in transaction"""
        return self._in_transaction


class Environment:
    """
    Odoo-like Environment providing access to cr, uid, context
    """
    
    def __init__(self, cr: DatabaseCursor, uid: int, context: Dict[str, Any] = None,
                 memory_registry: Optional['DatabaseMemoryRegistry'] = None):
        if cr is None:
            raise ValueError("Database cursor (cr) is mandatory")
        if uid is None:
            raise ValueError("User ID (uid) is mandatory")
        
        # Generate unique UUID for this environment instance
        self._uuid = str(uuid.uuid4())
        
        self._cr = cr
        self._uid = uid
        self._context = context or {}
        self._models = {}
        self._memory_registry = memory_registry
        
        # Get logger and log environment creation
        self._logger = get_logger(f"{__name__}.Environment")
        
        registry_info = f" with memory registry" if memory_registry else " without memory registry"
        self._logger.info(
            f"Environment created - UUID: {self._uuid}, DB: {cr.db_name}, UID: {uid}, "
            f"Context: {self._context}{registry_info}"
        )
        
        # Also log to console for easy debugging
        registry_status = " [REGISTRY]" if memory_registry else ""
        self._logger.debug(f"Environment UUID: {self._uuid} | DB: {cr.db_name} | UID: {uid}{registry_status}")
    
    @property
    def uuid(self) -> str:
        """Environment UUID"""
        return self._uuid

    @property
    def logger(self):
        """Logger property for backward compatibility"""
        return self._logger
    
    @property
    def cr(self) -> DatabaseCursor:
        """Database cursor"""
        return self._cr
    
    @property
    def uid(self) -> int:
        """User ID"""
        return self._uid
    
    @property
    def context(self) -> Dict[str, Any]:
        """Context dictionary"""
        return self._context.copy()
    
    @property
    def memory_registry(self) -> Optional['DatabaseMemoryRegistry']:
        """Memory registry for this environment's database"""
        return self._memory_registry
    
    @property
    def has_memory_registry(self) -> bool:
        """Check if this environment has a memory registry"""
        return self._memory_registry is not None
    
    def with_context(self, **context) -> 'Environment':
        """Create new environment with updated context"""
        new_context = {**self._context, **context}
        new_env = Environment(self._cr, self._uid, new_context, self._memory_registry)
        self._logger.debug(f"Environment {self._uuid} created child environment {new_env.uuid} with updated context")
        return new_env
    
    def with_user(self, uid: int) -> 'Environment':
        """Create new environment with different user"""
        new_env = Environment(self._cr, uid, self._context, self._memory_registry)
        self._logger.debug(f"Environment {self._uuid} created child environment {new_env.uuid} with different user {uid}")
        return new_env
    
    def __getitem__(self, model_name: str):
        """Get model by name (similar to Odoo's env['model.name'])"""
        # Models are no longer automatically registered
        # This method is kept for compatibility but will raise an error
        raise KeyError(f"Model {model_name} not found - automatic model loading has been removed")
    
    def ref(self, xml_id: str):
        """Get record by XML ID"""
        # TODO: Implement XML ID resolution
        # For now, return a placeholder
        parts = xml_id.split('.')
        if len(parts) == 2:
            module, xml_id = parts
            # This would normally query ir.model.data
            return None
        raise ValueError(f"Invalid XML ID format: {xml_id}")
    
    async def __aenter__(self):
        """Async context manager entry"""
        # Set this environment in the context
        ContextManager.set_environment(self)
        ContextManager.set_database(self._cr.db_name)
        ContextManager.set_user(self._uid)
        
        # Register with memory registry if available
        if self._memory_registry:
            await self._memory_registry.register_environment(self)
        
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        # Context will be automatically reset when the context var goes out of scope
        pass
    
    def __repr__(self):
        return f"Environment(uuid={self._uuid}, cr={self._cr.db_name}, uid={self._uid}, context={self._context})"


class EnvironmentManager:
    """
    Manager for creating and managing environments
    """
    
    @classmethod
    async def create_environment(
        cls,
        db_name: str,
        uid: int,
        context: Dict[str, Any] = None
    ) -> Environment:
        """Create a new environment"""
        from .database.registry import DatabaseRegistry
        from .database.memory import DatabaseFilterProcessor
        
        # Get logger for manager-level logging
        logger = get_logger(f"{__name__}.EnvironmentManager")
        logger.debug(f"Creating new environment for DB: {db_name}, UID: {uid}")
        
        # Check if memory registry should be created/used for this database
        memory_registry = await DatabaseFilterProcessor.ensure_registry_for_environment(db_name)
        
        # Get database manager
        db_manager = await DatabaseRegistry.get_database(db_name)
        
        # Create cursor
        cr = DatabaseCursor(db_manager, db_name)
        
        # Create environment with memory registry if applicable
        env = Environment(cr, uid, context, memory_registry)
        
        # Register environment with memory registry if available
        if memory_registry:
            await memory_registry.register_environment(env)
            logger.info(f"Environment {env.uuid} registered with memory registry for {db_name}")
        
        # Log successful creation at manager level
        logger.info(f"Environment {env.uuid} successfully created via EnvironmentManager")
        
        return env
    
    @classmethod
    def get_current_environment(cls) -> Optional[Environment]:
        """Get current environment from context"""
        return ContextManager.get_environment()
    
    @classmethod
    async def with_environment(
        cls, 
        db_name: str, 
        uid: int, 
        context: Dict[str, Any] = None
    ):
        """Context manager for running code with specific environment"""
        env = await cls.create_environment(db_name, uid, context)
        return ContextManager.environment.run(env)
    
    @classmethod
    @asynccontextmanager
    async def transaction(cls, env: Environment = None):
        """Context manager for database transactions"""
        if env is None:
            env = cls.get_current_environment()
            if env is None:
                raise RuntimeError("No environment found in context")
        
        async with env.cr._get_connection():
            await env.cr.begin()
            try:
                yield env
                await env.cr.commit()
            except Exception:
                await env.cr.rollback()
                raise


# Convenience function to get current environment
def env() -> Optional[Environment]:
    """Get current environment from context"""
    return EnvironmentManager.get_current_environment()


# Convenience function to create environment
async def create_env(db_name: str, uid: int, context: Dict[str, Any] = None) -> Environment:
    """Create a new environment"""
    return await EnvironmentManager.create_environment(db_name, uid, context)
