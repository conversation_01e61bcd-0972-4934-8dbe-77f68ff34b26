"""
Model and field definition models for base addon
"""
import os

# Import base model
from erp.models.base import BaseModel
from erp.fields import Char, <PERSON>, <PERSON>olean, Integer, Selection, One2Many, Many2One

# Ensure this addon is available via standardized import
from erp.addons import ensure_addon_import
addon_path = os.path.join(os.path.dirname(__file__), '..')
ensure_addon_import('base', addon_path)


class IrModel(BaseModel):
    """Model for storing model definitions"""

    _name = 'ir.model'
    _description = 'Model'
    _table = 'ir_model'
    _auto_create_table = False  # Prevent automatic table creation

    # Override name field to be more specific
    name = Char(string='Model Name', required=True, help='Technical name of the model (e.g., res.partner)')
    model = Char(string='Model', required=True, help='Model technical name')
    info = Text(string='Information', help='Model description')
    description = Text(string='Description', help='Model description (alias for info)')
    table = Char(string='Table Name', help='Database table name')
    state = Selection([
        ('manual', 'Custom Object'),
        ('base', 'Base Object'),
    ], string='Type', default='manual', required=True)

    transient = Boolean(string='Transient Model', default=False,
                       help='Whether this model is transient (temporary)')

    # One2many relationship to model fields
    field_ids = One2Many('ir.model.fields', 'model_id', string='Fields',
                        help='Fields defined for this model')

    async def _get_model_class(self):
        """Get the actual model class for this model"""
        try:
            from erp.database.memory import get_memory_registry_manager
            from erp.database.registry import DatabaseRegistry

            # Get current database
            current_db = DatabaseRegistry._current_db
            if not current_db:
                return None

            # Get memory registry for current database
            memory_manager = get_memory_registry_manager()
            registry = await memory_manager.get_registry(current_db)
            models = await registry.get_all_models()

            return models.get(self.model)
        except Exception:
            return None


class IrModelFields(BaseModel):
    """Model for storing field definitions"""

    _name = 'ir.model.fields'
    _description = 'Model Fields'
    _table = 'ir_model_fields'
    _auto_create_table = False  # Prevent automatic table creation

    # Override name field to be more specific
    name = Char(string='Field Name', required=True, help='Technical name of the field')
    field_description = Char(string='Field Label', required=True, help='Human readable label')
    help = Text(string='Field Help', help='Help text for the field')
    model = Char(string='Model', required=True, help='Model technical name')
    model_id = Many2One('ir.model', string='Model', help='Reference to ir.model record',
                       ondelete='cascade')

    ttype = Selection([
        ('char', 'Char'),
        ('text', 'Text'),
        ('integer', 'Integer'),
        ('float', 'Float'),
        ('boolean', 'Boolean'),
        ('date', 'Date'),
        ('datetime', 'Datetime'),
        ('selection', 'Selection'),
        ('many2one', 'Many2one'),
        ('one2many', 'One2many'),
        ('many2many', 'Many2many'),
    ], string='Field Type', required=True)

    required = Boolean(string='Required', default=False)
    readonly = Boolean(string='Readonly', default=False)
    index = Boolean(string='Indexed', default=False)
    store = Boolean(string='Store', default=True, help='Whether the field is stored in database')
    translate = Boolean(string='Translate', default=False, help='Whether the field is translatable')

    size = Integer(string='Size', help='Field size (for char fields)')
    digits = Text(string='Digits', help='Precision and scale for float fields (e.g., "16,2")')

    relation = Char(string='Relation', help='Related model for relational fields')
    relation_field = Char(string='Relation Field', help='Field name in related model')

    selection = Text(string='Selection Options', help='Selection options as string representation')
    domain = Text(string='Domain', default='[]', help='Domain filter for the field')
    context = Text(string='Context', default='{}', help='Context for the field')

    state = Selection([
        ('manual', 'Custom Field'),
        ('base', 'Base Field'),
    ], string='Type', default='manual', required=True)

    def _get_field_definition(self):
        """Get field definition for this field"""
        from erp.fields import (
            Char, Text, Integer, Float, Boolean, Date, Datetime,
            Selection, Many2One, One2Many, Many2Many
        )

        field_classes = {
            'char': Char,
            'text': Text,
            'integer': Integer,
            'float': Float,
            'boolean': Boolean,
            'date': Date,
            'datetime': Datetime,
            'selection': Selection,
            'many2one': Many2One,
            'one2many': One2Many,
            'many2many': Many2Many,
        }

        field_class = field_classes.get(self.ttype)
        if not field_class:
            return None

        kwargs = {
            'string': self.field_description,
            'required': self.required,
            'readonly': self.readonly,
            'help': self.help,
            'index': self.index,
            'store': self.store,
            'translate': self.translate,
        }

        # Add type-specific parameters
        if self.ttype == 'char' and self.size:
            kwargs['size'] = self.size
        elif self.ttype == 'float' and self.digits:
            # Parse digits string (e.g., "16,2" -> (16, 2))
            try:
                precision, scale = map(int, self.digits.split(','))
                kwargs['digits'] = (precision, scale)
            except (ValueError, AttributeError):
                pass
        elif self.ttype == 'selection' and self.selection:
            # Parse selection string (simplified)
            kwargs['selection'] = eval(self.selection) if self.selection else []
        elif self.ttype in ('many2one', 'one2many', 'many2many') and self.relation:
            kwargs['comodel_name'] = self.relation
            if self.ttype == 'one2many' and self.relation_field:
                kwargs['inverse_name'] = self.relation_field

        # Add domain and context if provided
        if self.domain and self.domain != '[]':
            try:
                kwargs['domain'] = eval(self.domain)
            except (ValueError, SyntaxError):
                pass

        if self.context and self.context != '{}':
            try:
                kwargs['context'] = eval(self.context)
            except (ValueError, SyntaxError):
                pass

        return field_class(**kwargs)

# The models are automatically registered via the metaclass
