"""
Common response utilities to eliminate duplicate code patterns
"""
from typing import Any, Dict, Optional
from fastapi import HTTPException
from fastapi.responses import JSONResponse


class APIResponse:
    """Standardized API response utilities"""
    
    @staticmethod
    def success(data: Any = None, message: str = "Success") -> Dict[str, Any]:
        """Create success response"""
        response = {"success": True, "message": message}
        if data is not None:
            response["data"] = data
        return response
    
    @staticmethod
    def error(message: str, code: int = 500, details: Any = None) -> HTTPException:
        """Create error response"""
        error_data = {"error": message}
        if details:
            error_data["details"] = details
        raise HTTPException(status_code=code, detail=error_data)
    
    @staticmethod
    def not_found(resource: str = "Resource") -> HTTPException:
        """Create not found response"""
        return APIResponse.error(f"{resource} not found", 404)
    
    @staticmethod
    def validation_error(message: str, details: Any = None) -> HTTPException:
        """Create validation error response"""
        return APIResponse.error(f"Validation error: {message}", 422, details)
    
    @staticmethod
    def server_error(message: str = "Internal server error") -> HTTPException:
        """Create server error response"""
        return APIResponse.error(message, 500)


class ModelResponse:
    """Model-specific response utilities"""
    
    @staticmethod
    def model_not_found(model_name: str) -> HTTPException:
        """Model not found error"""
        return APIResponse.error(f"Model '{model_name}' not found", 404)
    
    @staticmethod
    def method_not_found(model_name: str, method: str) -> HTTPException:
        """Method not found error"""
        return APIResponse.error(
            f"Method '{method}' not found on model '{model_name}'", 404
        )
    
    @staticmethod
    def model_result(result: Any) -> Dict[str, Any]:
        """Wrap model operation result"""
        return {"result": result}
    
    @staticmethod
    def success(data: Any = None, message: str = "Success") -> Dict[str, Any]:
        """Create success response for model operations"""
        return APIResponse.success(data, message)


def handle_database_error(e: Exception) -> HTTPException:
    """Handle database errors consistently"""
    error_msg = str(e)
    
    # Map specific database errors to appropriate HTTP status codes
    if "does not exist" in error_msg.lower():
        return APIResponse.error("Resource not found", 404)
    elif "duplicate key" in error_msg.lower():
        return APIResponse.error("Resource already exists", 409)
    elif "permission denied" in error_msg.lower():
        return APIResponse.error("Access denied", 403)
    else:
        return APIResponse.server_error(f"Database error: {error_msg}")


def handle_generic_error(e: Exception) -> HTTPException:
    """Handle generic errors consistently"""
    return APIResponse.server_error(str(e))
