"""
Base model with database integration
"""
import uuid
import time
import logging
import re
from datetime import datetime
from typing import Dict, Any, Optional, List, Type, Union
from ..fields import Field, Char, Datetime
from ..database.registry import DatabaseRegistry
from ..utils.domain import DomainFilter
from ..logging import get_logger


class ModelMeta(type):
    """Metaclass for models to handle field definitions"""
    
    def __new__(cls, name, bases, attrs):
        # Collect fields from the class and its parents
        fields = {}
        
        # Get fields from parent classes
        for base in bases:
            if hasattr(base, '_fields'):
                fields.update(base._fields)
        
        # Get fields from current class
        for key, value in list(attrs.items()):
            if isinstance(value, Field):
                fields[key] = value
                # Remove field from class attributes to avoid conflicts
                del attrs[key]
        
        # Store fields in the class
        attrs['_fields'] = fields
        
        # Create the class
        new_class = super().__new__(cls, name, bases, attrs)
        
        return new_class


class BaseModel(metaclass=ModelMeta):
    """Base model class with database integration"""

    _name = None  # Model name (to be overridden in subclasses)
    _description = None  # Model description
    _table = None  # Database table name

    # Common fields for all models
    id = Char(string='ID', required=True, readonly=True, default=lambda: str(uuid.uuid4()))
    name = Char(string='Name', required=True)
    createAt = Datetime(string='Created At', readonly=True, default=lambda: datetime.now())
    updateAt = Datetime(string='Updated At', readonly=True, default=lambda: datetime.now())

    def __init__(self, **kwargs):
        self._values = {}
        self._is_new_record = True
        self._logger = get_logger(f"{__name__}.{self.__class__._name or self.__class__.__name__}")
        
        # Initialize fields with default values
        for field_name, field in self._fields.items():
            if field_name in kwargs:
                self._values[field_name] = kwargs[field_name]
            else:
                default_value = field.get_default_value()
                if default_value is not None:
                    self._values[field_name] = default_value
    
    def __getattr__(self, name):
        """Get field value with support for relational fields"""
        if name in self._fields:
            field = self._fields[name]
            value = self._values.get(name)

            # Handle relational fields
            if hasattr(field, 'comodel_name'):
                from ..fields import Many2One, One2Many, Many2Many, One2One

                if isinstance(field, Many2One) or isinstance(field, One2One):
                    # Return a RecordSet for the related record
                    if value:
                        return self._get_related_record(field.comodel_name, value)
                    return None

                elif isinstance(field, (One2Many, Many2Many)):
                    # Return a RecordSet for related records
                    return self._get_related_records(field, name)

            return value
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")
    
    def __setattr__(self, name, value):
        """Set field value"""
        if name.startswith('_') or name in ('_fields', '_name', '_description', '_table'):
            super().__setattr__(name, value)
        elif hasattr(self, '_fields') and name in self._fields:
            field = self._fields[name]
            if field.readonly and not self._is_new_record:
                raise ValueError(f"Field '{name}' is readonly")
            validated_value = field.validate(value)
            self._values[name] = validated_value
        else:
            super().__setattr__(name, value)

    def _get_related_record(self, comodel_name, record_id):
        """Get a related record for Many2One/One2One fields"""
        # This is a placeholder - in a full implementation, this would
        # use a model registry to get the actual model class and create a RecordSet
        from .recordset import RecordSet

        # For now, return a mock RecordSet with the ID
        # In a real implementation, this would load the actual record
        class MockRecord:
            def __init__(self, id_val):
                self.id = id_val
                self._name = comodel_name

        mock_record = MockRecord(record_id)
        return RecordSet(None, [mock_record])

    def _get_related_records(self, field, field_name):
        """Get related records for One2many/Many2many fields"""
        from ..fields import One2Many, Many2Many
        from .recordset import RecordSet

        if isinstance(field, One2Many):
            # For One2many, we need to search the related model for records
            # that have this record's ID in their inverse field
            # This is a placeholder implementation
            return RecordSet(None, [])

        elif isinstance(field, Many2Many):
            # For Many2many, we need to query the intersection table
            # This is a placeholder implementation
            return RecordSet(None, [])

        return RecordSet(None, [])

    async def _process_relational_commands(self, relational_data, db):
        """Process relational field commands after record creation/update"""
        from ..fields import One2Many, Many2Many

        for field_name, commands in relational_data.items():
            field = self._fields[field_name]

            if isinstance(field, One2Many):
                await self._process_one2many_commands(field, field_name, commands, db)
            elif isinstance(field, Many2Many):
                await self._process_many2many_commands(field, field_name, commands, db)

    async def _process_one2many_commands(self, field, field_name, commands, db):
        """Process One2many field commands"""
        # This is a placeholder implementation
        # In a real implementation, this would:
        # - Handle CREATE (0) commands by creating new records
        # - Handle UPDATE (1) commands by updating existing records
        # - Handle DELETE (2) commands by deleting records
        # - Handle UNLINK (3) commands by unlinking records
        # - Handle LINK (4) commands by linking existing records
        # - Handle CLEAR (5) commands by clearing all links
        # - Handle SET (6) commands by replacing all links
        pass

    async def _process_many2many_commands(self, field, field_name, commands, db):
        """Process Many2many field commands"""
        # This is a placeholder implementation
        # In a real implementation, this would:
        # - Handle CREATE (0) commands by creating new records and linking them
        # - Handle UPDATE (1) commands by updating existing records
        # - Handle DELETE (2) commands by deleting records and removing links
        # - Handle UNLINK (3) commands by removing links from intersection table
        # - Handle LINK (4) commands by adding links to intersection table
        # - Handle CLEAR (5) commands by clearing all links from intersection table
        # - Handle SET (6) commands by replacing all links in intersection table
        pass

    @staticmethod
    def _camel_to_snake_case(name: str) -> str:
        """Convert camelCase to snake_case for database column names"""
        # Insert an underscore before any uppercase letter that follows a lowercase letter or digit
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
        # Insert an underscore before any uppercase letter that follows a lowercase letter
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()

    @classmethod
    def _field_to_column_mapping(cls) -> Dict[str, str]:
        """Get mapping of field names to database column names"""
        mapping = {}
        for field_name in cls._fields.keys():
            mapping[field_name] = cls._camel_to_snake_case(field_name)
        return mapping

    @classmethod
    def _convert_data_to_db_columns(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert field names in data dict to database column names"""
        db_data = {}
        for field_name, value in data.items():
            if field_name in cls._fields:
                db_column_name = cls._camel_to_snake_case(field_name)
                db_data[db_column_name] = value
            else:
                # Keep non-field keys as-is (like 'id')
                db_data[field_name] = value
        return db_data

    @classmethod
    def _convert_db_row_to_field_data(cls, row_data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert database row data with column names back to field names"""
        field_data = {}
        column_to_field_mapping = {cls._camel_to_snake_case(field_name): field_name
                                   for field_name in cls._fields.keys()}

        for column_name, value in row_data.items():
            if column_name in column_to_field_mapping:
                field_name = column_to_field_mapping[column_name]
                field_data[field_name] = value
            else:
                # Keep non-field columns as-is (like 'id')
                field_data[column_name] = value
        return field_data

    @classmethod
    def _convert_domain_to_db_columns(cls, domain: List) -> List:
        """Convert field names in domain to database column names"""
        if not domain:
            return domain

        converted_domain = []
        for condition in domain:
            if len(condition) == 3:
                field_name, operator, value = condition
                if field_name in cls._fields:
                    db_column_name = cls._camel_to_snake_case(field_name)
                    converted_domain.append([db_column_name, operator, value])
                else:
                    # Keep non-field names as-is (like 'id')
                    converted_domain.append(condition)
            else:
                # Keep non-standard conditions as-is
                converted_domain.append(condition)
        return converted_domain

    @classmethod
    def _convert_order_to_db_columns(cls, order: str) -> str:
        """Convert field names in ORDER BY clause to database column names"""
        if not order:
            return order

        # Split by comma to handle multiple order fields
        order_parts = []
        for part in order.split(','):
            part = part.strip()
            # Check if it has ASC/DESC
            if ' ' in part:
                field_part, direction = part.rsplit(' ', 1)
                field_part = field_part.strip()
                direction = direction.strip()
                if field_part in cls._fields:
                    db_column_name = cls._camel_to_snake_case(field_part)
                    order_parts.append(f"{db_column_name} {direction}")
                else:
                    order_parts.append(part)
            else:
                # No direction specified
                if part in cls._fields:
                    db_column_name = cls._camel_to_snake_case(part)
                    order_parts.append(db_column_name)
                else:
                    order_parts.append(part)

        return ', '.join(order_parts)

    @classmethod
    async def create(cls, vals: Dict[str, Any]):
        """Create a new record in database"""
        logger = get_logger(f"{__name__}.{cls._name or cls.__name__}")
        start_time = time.perf_counter()

        logger.debug(f"Creating new {cls._name} record")

        record = cls(**vals)

        # Get database manager
        db = await DatabaseRegistry.get_current_database()
        if not db:
            logger.error(f"No database connection available for {cls._name} create")
            raise RuntimeError("No database connection available")

        # Prepare data for insertion
        insert_data = {}
        relational_data = {}

        for field_name, field in cls._fields.items():
            if field_name in record._values:
                from ..fields import Many2One, One2Many, Many2Many, One2One

                # Handle relational fields differently
                if isinstance(field, (Many2One, One2One)):
                    # Store the ID for Many2One/One2One fields
                    insert_data[field_name] = record._values[field_name]

                elif isinstance(field, (One2Many, Many2Many)):
                    # Store relational commands for post-processing
                    relational_data[field_name] = record._values[field_name]

                else:
                    # Regular field
                    insert_data[field_name] = record._values[field_name]

        # Insert into database
        table_name = cls._table or cls._name.replace('.', '_')
        logger.debug(f"Inserting into table: {table_name}")

        # Convert field names to database column names
        db_insert_data = cls._convert_data_to_db_columns(insert_data)
        record_id = await db.insert(table_name, db_insert_data)

        if record_id:
            record._values['id'] = str(record_id)
            logger.debug(f"Record created with ID: {record_id}")
        else:
            logger.warning(f"No ID returned from insert for {cls._name}")

        record._is_new_record = False

        # Process relational field commands
        if relational_data:
            await record._process_relational_commands(relational_data, db)

        duration = time.perf_counter() - start_time
        logger.debug(f"Created {cls._name} record in {duration:.3f}s")

        return record



    @classmethod
    async def search(cls, domain: List = None, limit: int = None, offset: int = 0, order: str = None):
        """Search records matching domain"""
        logger = get_logger(f"{__name__}.{cls._name or cls.__name__}")
        start_time = time.perf_counter()

        logger.debug(f"Searching {cls._name} with domain: {domain}")

        db = await DatabaseRegistry.get_current_database()
        if not db:
            logger.error(f"No database connection available for {cls._name} search")
            raise RuntimeError("No database connection available")

        table_name = cls._table or cls._name.replace('.', '_')

        # Build query
        query = f"SELECT * FROM {table_name}"
        params = []

        # Add WHERE clause for domain using DomainFilter
        if domain:
            # Convert field names in domain to database column names
            db_domain = cls._convert_domain_to_db_columns(domain)
            where_clause, domain_params = DomainFilter.build_sql_where_clause(db_domain, len(params))
            if where_clause:
                query += " WHERE " + where_clause
                params.extend(domain_params)

        # Add ORDER BY clause
        if order:
            # Convert field names in order clause to database column names
            db_order = cls._convert_order_to_db_columns(order)
            query += f" ORDER BY {db_order}"

        # Add LIMIT and OFFSET
        if limit:
            query += f" LIMIT {limit}"
        if offset:
            query += f" OFFSET {offset}"

        logger.debug(f"Executing query: {query}")

        # Execute query
        rows = await db.fetch(query, *params)

        # Convert rows to model instances
        records = []
        for row in rows:
            record = cls()
            # Convert database column names back to field names
            record._values = cls._convert_db_row_to_field_data(dict(row))
            record._is_new_record = False
            records.append(record)

        duration = time.perf_counter() - start_time
        logger.debug(f"Found {len(records)} {cls._name} records in {duration:.3f}s")

        # Import RecordSet locally to avoid circular imports
        from .recordset import RecordSet
        return RecordSet(cls, records)
    
    @classmethod
    async def browse(cls, ids: Union[str, List[str]]):
        """Browse records by IDs"""
        if isinstance(ids, str):
            ids = [ids]

        if not ids:
            # Import RecordSet locally to avoid circular imports
            from .recordset import RecordSet
            return RecordSet(cls, [])

        return await cls.search([('id', 'in', ids)])
    
    async def write(self, vals: Dict[str, Any]):
        """Update record with new values"""
        start_time = time.perf_counter()
        record_id = self._values.get('id')

        self._logger.debug(f"Updating {self._name} record {record_id}")

        if self._is_new_record:
            self._logger.error(f"Cannot update a new {self._name} record. Use create() instead.")
            raise ValueError("Cannot update a new record. Use create() instead.")

        # Update values
        for field_name, value in vals.items():
            if field_name in self._fields:
                setattr(self, field_name, value)

        # Update updateAt timestamp
        if 'updateAt' in self._fields:
            self._values['updateAt'] = datetime.now()

        # Save to database
        db = await DatabaseRegistry.get_current_database()
        if not db:
            self._logger.error(f"No database connection available for {self._name} update")
            raise RuntimeError("No database connection available")

        table_name = self._table or self._name.replace('.', '_')

        if not record_id:
            self._logger.error(f"Record has no ID for update in {self._name}")
            raise ValueError("Record has no ID for update")

        # Prepare update data
        update_data = {}
        for field_name, value in vals.items():
            if field_name in self._fields:
                update_data[field_name] = self._values[field_name]

        # Add updateAt if it exists
        if 'updateAt' in self._fields:
            update_data['updateAt'] = self._values['updateAt']

        # Convert field names to database column names
        db_update_data = self._convert_data_to_db_columns(update_data)
        await db.update(table_name, record_id, db_update_data)

        duration = time.perf_counter() - start_time
        self._logger.debug(f"Updated {self._name} record {record_id} in {duration:.3f}s")

        return True
    
    async def unlink(self):
        """Delete record from database"""
        if self._is_new_record:
            raise ValueError("Cannot delete a new record")
        
        db = await DatabaseRegistry.get_current_database()
        if not db:
            raise RuntimeError("No database connection available")
        
        table_name = self._table or self._name.replace('.', '_')
        record_id = self._values.get('id')
        
        if not record_id:
            raise ValueError("Record has no ID for deletion")
        
        await db.delete(table_name, record_id)
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert record to dictionary"""
        return self._values.copy()
    
    def __repr__(self):
        name = self._values.get('name', 'Unknown')
        record_id = self._values.get('id', 'New')
        return f"<{self.__class__.__name__}({record_id}): {name}>"
