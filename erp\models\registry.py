"""
Lifecycle-bound ModelRegistry for addon operations

This module provides a temporary ModelRegistry that is created during addon
installation, upgrade, and uninstallation processes. It discovers models
from a specific addon and provides them for schema synchronization and
IR metadata population.

The ModelRegistry is NOT a global singleton - it's created per addon operation
and disposed of when the operation completes.
"""
import importlib
import inspect
import os
import sys
from typing import Dict, List, Optional, Type, Any, Set
from pathlib import Path

from ..logging import get_logger
from .base import BaseModel


class ModelRegistry:
    """
    Lifecycle-bound model registry for addon operations
    
    This registry is temporary and addon-specific. It's created when an addon
    operation begins and disposed of when the operation completes.
    
    Key characteristics:
    - Lifecycle-bound: Only exists during addon operations
    - Temporary scope: Created and disposed per operation
    - Addon-specific: Tracks models only for the specific addon being processed
    """
    
    def __init__(self, addon_name: str):
        """
        Initialize registry for a specific addon
        
        Args:
            addon_name: Name of the addon to discover models for
        """
        self.addon_name = addon_name
        self.logger = get_logger(f"{__name__}.{addon_name}")
        self._models: Dict[str, Type[BaseModel]] = {}
        self._discovered = False
        
        self.logger.debug(f"Created ModelRegistry for addon: {addon_name}")
    
    def discover_models(self) -> None:
        """
        Discover all models in the addon by importing its modules
        and scanning for BaseModel subclasses
        """
        if self._discovered:
            self.logger.debug(f"Models already discovered for addon: {self.addon_name}")
            return
        
        self.logger.info(f"Discovering models for addon: {self.addon_name}")
        
        try:
            # Handle special case for base addon
            if self.addon_name == 'base':
                self._discover_base_models()
            else:
                self._discover_addon_models()
            
            self._discovered = True
            self.logger.info(f"Discovered {len(self._models)} models for addon: {self.addon_name}")
            
        except Exception as e:
            self.logger.error(f"Failed to discover models for addon {self.addon_name}: {e}")
            raise
    
    def _discover_base_models(self) -> None:
        """Discover models in the base addon"""
        base_models_path = "addons.base.models"
        
        try:
            # Import the base models package
            models_package = importlib.import_module(base_models_path)
            package_dir = Path(models_package.__file__).parent
            
            # Scan all Python files in the models directory
            for py_file in package_dir.glob("*.py"):
                if py_file.name.startswith("__"):
                    continue
                
                module_name = f"{base_models_path}.{py_file.stem}"
                try:
                    module = importlib.import_module(module_name)
                    self._scan_module_for_models(module)
                except Exception as e:
                    self.logger.warning(f"Failed to import {module_name}: {e}")
                    
        except Exception as e:
            self.logger.error(f"Failed to discover base models: {e}")
            raise
    
    def _discover_addon_models(self) -> None:
        """Discover models in a regular addon"""
        addon_module_path = f"addons.{self.addon_name}"
        
        try:
            # Try to import the addon package
            try:
                addon_package = importlib.import_module(addon_module_path)
            except ImportError:
                # Try alternative path
                addon_module_path = f"erp.addons.{self.addon_name}"
                addon_package = importlib.import_module(addon_module_path)
            
            # Check if addon has a models subpackage
            models_module_path = f"{addon_module_path}.models"
            try:
                models_package = importlib.import_module(models_module_path)
                package_dir = Path(models_package.__file__).parent
                
                # Scan all Python files in the models directory
                for py_file in package_dir.glob("*.py"):
                    if py_file.name.startswith("__"):
                        continue
                    
                    module_name = f"{models_module_path}.{py_file.stem}"
                    try:
                        module = importlib.import_module(module_name)
                        self._scan_module_for_models(module)
                    except Exception as e:
                        self.logger.warning(f"Failed to import {module_name}: {e}")
                        
            except ImportError:
                # No models subpackage, scan the main addon module
                self._scan_module_for_models(addon_package)
                
        except ImportError as e:
            self.logger.warning(f"Addon {self.addon_name} not found or has no Python modules: {e}")
    
    def _scan_module_for_models(self, module) -> None:
        """
        Scan a module for BaseModel subclasses
        
        Args:
            module: Python module to scan
        """
        for name in dir(module):
            obj = getattr(module, name)
            
            # Check if it's a class that inherits from BaseModel
            if (inspect.isclass(obj) and 
                issubclass(obj, BaseModel) and 
                obj is not BaseModel and
                hasattr(obj, '_name') and 
                obj._name):
                
                model_name = obj._name
                self._models[model_name] = obj
                self.logger.debug(f"Discovered model: {model_name} ({obj.__name__})")
    
    def all(self) -> Dict[str, Type[BaseModel]]:
        """
        Get all discovered models
        
        Returns:
            Dictionary mapping model names to model classes
        """
        if not self._discovered:
            self.discover_models()
        
        return self._models.copy()
    
    def get(self, model_name: str) -> Optional[Type[BaseModel]]:
        """
        Get a specific model by name
        
        Args:
            model_name: Technical name of the model
            
        Returns:
            Model class or None if not found
        """
        if not self._discovered:
            self.discover_models()
        
        return self._models.get(model_name)
    
    def get_model_names(self) -> List[str]:
        """
        Get list of all model names in this addon

        Returns:
            List of model names
        """
        if not self._discovered:
            self.discover_models()

        return list(self._models.keys())

    def get_model_fields(self, model_name: str) -> Dict[str, Any]:
        """
        Get all fields for a specific model

        Args:
            model_name: Technical name of the model

        Returns:
            Dictionary mapping field names to field objects
        """
        model_class = self.get(model_name)
        if not model_class:
            return {}

        return getattr(model_class, '_fields', {})

    def get_all_models_with_fields(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all models with their field information

        Returns:
            Dictionary mapping model names to their field dictionaries
        """
        if not self._discovered:
            self.discover_models()

        result = {}
        for model_name, model_class in self._models.items():
            result[model_name] = {
                'class': model_class,
                'fields': getattr(model_class, '_fields', {}),
                'table': getattr(model_class, '_table', None) or model_name.replace('.', '_'),
                'description': getattr(model_class, '_description', None) or model_name
            }

        return result
    
    def has_models(self) -> bool:
        """
        Check if this addon has any models
        
        Returns:
            True if addon has models, False otherwise
        """
        if not self._discovered:
            self.discover_models()
        
        return len(self._models) > 0
    
    def clear(self) -> None:
        """
        Clear the registry (for cleanup)
        """
        self._models.clear()
        self._discovered = False
        self.logger.debug(f"Cleared ModelRegistry for addon: {self.addon_name}")
    
    def __len__(self) -> int:
        """Get number of models in registry"""
        if not self._discovered:
            self.discover_models()
        return len(self._models)
    
    def __contains__(self, model_name: str) -> bool:
        """Check if model exists in registry"""
        if not self._discovered:
            self.discover_models()
        return model_name in self._models
    
    def __repr__(self) -> str:
        """String representation"""
        return f"<ModelRegistry(addon={self.addon_name}, models={len(self._models)})>"



