"""
FastAPI ASGI Server for ERP system - Modularized version
"""
from fastapi import FastAPI

from .server_config import ServerConfig
from .routes import api, database, system
from .utils.responses import APIResponse
from .http import setup_http_routes


class ERPServer:
    """Main ERP server application"""

    def __init__(self):
        """Initialize ERP server"""
        self.server_config = ServerConfig()
        self.app = self._create_app()
        self._setup_server()
        self._setup_routes()
    
    def _create_app(self) -> FastAPI:
        """Create FastAPI application"""
        return self.server_config.create_app()
    
    def _setup_server(self):
        """Setup server configuration"""
        self.server_config.setup_static_files(self.app)
        self.server_config.setup_middleware(self.app)
        
        # Set FastAPI app instance in MemoryRegistryManager for route registration
        from .database.memory.registry_manager import MemoryRegistryManager
        MemoryRegistryManager.set_app(self.app)
    
    def _setup_routes(self):
        """Setup system routes"""
        # Include system route modules
        self.app.include_router(system.router)
        self.app.include_router(api.router)
        self.app.include_router(database.view_router)  # Database view routes only

        # Update system routes
        self._update_system_routes()
    

    
    def _update_system_routes(self):
        """Update system routes with server-specific data"""
        # Update health check
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint - only checks server status, not database connections"""
            try:
                # Only check server health, no database connections during startup
                return APIResponse.success({
                    "status": "healthy",
                    "server": "running",
                    "message": "ERP server is running"
                })
            except Exception as e:
                return APIResponse.error(f"Health check failed: {str(e)}", status_code=503)
        
        # Add a test route to verify route registration works
        @self.app.get("/test-direct")
        async def test_direct():
            """Direct test route to verify FastAPI routing works"""
            from fastapi.responses import JSONResponse
            return JSONResponse(content={
                "status": "success",
                "message": "Direct route registration works",
                "route": "/test-direct"
            })


# Create global server instance
server = ERPServer()
app = server.app


def create_app():
    """Create and return the FastAPI application"""
    return app
