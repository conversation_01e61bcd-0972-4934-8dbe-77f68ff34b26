"""
Common middleware utilities
"""
from fastapi import Request
from typing import Callable, Any
from .middleware.database import DatabaseMiddleware
from .middleware.timing import TimingMiddleware
from .middleware.error_handling import ErrorHandlingMiddleware
from .middleware.logging import LoggingMiddleware
from .middleware.environment import EnvironmentMiddleware
from .middleware.transaction import TransactionMiddleware
from ..logging import get_logger

logger = get_logger(__name__)

async def database_middleware(request: Request, call_next: Callable) -> Any:
    return await DatabaseMiddleware.process_request(request, call_next)

async def timing_middleware(request: Request, call_next: Callable) -> Any:
    return await TimingMiddleware.process_request(request, call_next)

async def transaction_middleware(request: Request, call_next: Callable) -> Any:
    return await TransactionMiddleware.process_request(request, call_next)

async def error_handling_middleware(request: Request, call_next: Callable) -> Any:
    return await ErrorHandlingMiddleware.process_request(request, call_next)

async def logging_middleware(request: Request, call_next: Callable) -> Any:
    return await LoggingMiddleware.process_request(request, call_next)

async def environment_middleware(request: Request, call_next: Callable) -> Any:
    return await EnvironmentMiddleware.process_request(request, call_next)
