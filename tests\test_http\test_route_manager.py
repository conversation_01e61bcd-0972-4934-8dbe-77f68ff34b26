"""
Test suite for RouteManager functionality

This module tests:
- Route registration and management
- Route retrieval and removal
- Route statistics and operations
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

from erp.database.memory import RouteManager


class TestRouteManager:
    """Test RouteManager functionality"""
    
    def test_route_manager_initialization(self):
        """Test RouteManager initialization"""
        manager = RouteManager("test_db")
        
        assert manager.db_name == "test_db"
        assert manager.routes == {}
        assert manager._routes_registered is False
    
    @pytest.mark.asyncio
    async def test_register_route(self):
        """Test route registration"""
        manager = RouteManager("test_db")
        
        async def test_handler():
            return "test response"
        
        await manager.register_route("/test", test_handler, methods=["GET"])
        
        routes = await manager.get_routes()
        assert "/test" in routes
        assert routes["/test"]["handler"] == test_handler
        assert routes["/test"]["path"] == "/test"
    
    @pytest.mark.asyncio
    async def test_get_route(self):
        """Test getting specific route"""
        manager = RouteManager("test_db")
        
        async def test_handler():
            return "test"
        
        await manager.register_route("/test", test_handler)
        
        route = await manager.get_route("/test")
        assert route is not None
        assert route["handler"] == test_handler
    
    @pytest.mark.asyncio
    async def test_get_route_nonexistent(self):
        """Test getting nonexistent route"""
        manager = RouteManager("test_db")
        
        route = await manager.get_route("/nonexistent")
        assert route is None
    
    @pytest.mark.asyncio
    async def test_remove_route(self):
        """Test removing route"""
        manager = RouteManager("test_db")
        
        async def test_handler():
            return "test"
        
        await manager.register_route("/test", test_handler)
        
        # Verify route exists
        routes = await manager.get_routes()
        assert "/test" in routes
        
        # Remove route
        result = await manager.remove_route("/test")
        assert result is True
        
        # Verify route is removed
        routes = await manager.get_routes()
        assert "/test" not in routes
    
    @pytest.mark.asyncio
    async def test_remove_nonexistent_route(self):
        """Test removing nonexistent route"""
        manager = RouteManager("test_db")
        
        result = await manager.remove_route("/nonexistent")
        assert result is False
    
    def test_get_route_stats(self):
        """Test getting route statistics"""
        manager = RouteManager("test_db")
        
        # Add some test routes
        manager.routes = {
            "/route1": {"handler": lambda: None},
            "/route2": {"handler": lambda: None}
        }
        
        stats = manager.get_route_stats()
        
        assert 'routes_count' in stats
        assert 'routes_registered' in stats
        assert stats['routes_count'] == 2
        assert isinstance(stats['routes_registered'], bool)
    
    @pytest.mark.asyncio
    async def test_register_multiple_routes(self):
        """Test registering multiple routes"""
        manager = RouteManager("test_db")
        
        async def handler1():
            return "response1"
        
        async def handler2():
            return "response2"
        
        await manager.register_route("/route1", handler1, methods=["GET"])
        await manager.register_route("/route2", handler2, methods=["POST"])
        
        routes = await manager.get_routes()
        assert len(routes) == 2
        assert "/route1" in routes
        assert "/route2" in routes
        assert routes["/route1"]["handler"] == handler1
        assert routes["/route2"]["handler"] == handler2
    
    @pytest.mark.asyncio
    async def test_route_overwrite(self):
        """Test overwriting existing route"""
        manager = RouteManager("test_db")
        
        async def handler1():
            return "response1"
        
        async def handler2():
            return "response2"
        
        # Register initial route
        await manager.register_route("/test", handler1)
        
        # Overwrite with new handler
        await manager.register_route("/test", handler2)
        
        route = await manager.get_route("/test")
        assert route["handler"] == handler2
    
    @pytest.mark.asyncio
    async def test_route_methods_handling(self):
        """Test route registration with different HTTP methods"""
        manager = RouteManager("test_db")
        
        async def get_handler():
            return "GET response"
        
        async def post_handler():
            return "POST response"
        
        await manager.register_route("/api/test", get_handler, methods=["GET"])
        await manager.register_route("/api/create", post_handler, methods=["POST"])
        
        routes = await manager.get_routes()
        assert "/api/test" in routes
        assert "/api/create" in routes
    
    def test_route_stats_empty(self):
        """Test route statistics when no routes are registered"""
        manager = RouteManager("test_db")
        
        stats = manager.get_route_stats()
        
        assert stats['routes_count'] == 0
        assert stats['routes_registered'] is False
