"""
Cache management for database memory registry
"""
import time
from typing import Dict, Optional, Any, Tuple
from ...logging import get_logger


class QueryCache:
    """Simple query cache with TTL support"""

    def __init__(self, max_size: int = 1000, ttl: int = 300):
        self.max_size = max_size
        self.ttl = ttl  # Time to live in seconds
        self._cache: Dict[str, Tuple[Any, float]] = {}  # key -> (value, timestamp)
        self._access_order: Dict[str, float] = {}  # key -> last_access_time

    def get(self, key: str) -> Optional[Any]:
        """Get item from cache"""
        if key not in self._cache:
            return None

        value, timestamp = self._cache[key]

        # Check if expired
        if time.time() - timestamp > self.ttl:
            self.remove(key)
            return None

        # Update access time
        self._access_order[key] = time.time()
        return value

    def set(self, key: str, value: Any) -> None:
        """Set item in cache"""
        current_time = time.time()

        # Remove oldest items if cache is full
        if len(self._cache) >= self.max_size and key not in self._cache:
            self._evict_oldest()

        self._cache[key] = (value, current_time)
        self._access_order[key] = current_time

    def remove(self, key: str) -> None:
        """Remove item from cache"""
        self._cache.pop(key, None)
        self._access_order.pop(key, None)

    def clear(self) -> None:
        """Clear all cache"""
        self._cache.clear()
        self._access_order.clear()

    def _evict_oldest(self) -> None:
        """Evict the least recently used item"""
        if not self._access_order:
            return

        oldest_key = min(self._access_order.keys(), key=lambda k: self._access_order[k])
        self.remove(oldest_key)


class CacheManager:
    """Manages query caching for database registry"""

    def __init__(self, db_name: str, max_size: int = 1000, ttl: int = 300):
        self.db_name = db_name
        self.query_cache = QueryCache(max_size=max_size, ttl=ttl)
        self._logger = get_logger(f"{__name__}.{db_name}")

    async def cache_query(self, query_key: str, result: Any) -> None:
        """Cache a query result"""
        self.query_cache.set(query_key, result)
        self._logger.debug(f"Cached query result for key: {query_key}")

    async def get_cached_query(self, query_key: str) -> Optional[Any]:
        """Get a cached query result"""
        result = self.query_cache.get(query_key)
        if result is not None:
            self._logger.debug(f"Cache hit for key: {query_key}")
        return result

    async def clear_query_cache(self) -> None:
        """Clear the query cache"""
        self.query_cache.clear()
        self._logger.debug("Query cache cleared")

    async def invalidate_model_cache(self, model_name: str) -> None:
        """Invalidate cache for a specific model"""
        # In a more sophisticated implementation, we would only clear
        # cache entries related to this model, but for simplicity
        # we clear the entire cache
        await self.clear_query_cache()
        self._logger.debug(f"Invalidated cache for model: {model_name}")

    async def execute_query_cached(self, query_key: str, query_func, *args, **kwargs) -> Any:
        """
        Execute a query with caching

        Args:
            query_key: Cache key for the query
            query_func: Async function to execute if cache miss
            *args: Arguments to pass to query_func
            **kwargs: Keyword arguments to pass to query_func

        Returns:
            Query result (from cache or fresh execution)
        """
        if not query_key:
            self._logger.warning("Empty query key provided, executing without cache")
            return await query_func(*args, **kwargs)

        try:
            # Check cache first
            cached_result = await self.get_cached_query(query_key)
            if cached_result is not None:
                return cached_result

            # Execute query
            result = await query_func(*args, **kwargs)

            # Cache result if not None
            if result is not None:
                await self.cache_query(query_key, result)

            return result
        except Exception as e:
            self._logger.error(f"Error in cached query execution for key {query_key}: {e}")
            # Fallback to direct execution
            return await query_func(*args, **kwargs)

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            'max_size': self.query_cache.max_size,
            'ttl': self.query_cache.ttl,
            'current_size': len(self.query_cache._cache),
            'hit_ratio': self._calculate_hit_ratio()
        }

    def _calculate_hit_ratio(self) -> float:
        """Calculate cache hit ratio (placeholder implementation)"""
        # In a real implementation, we would track hits and misses
        return 0.0
