"""
Logging Handlers - Custom handlers for different output destinations
"""
import logging
import logging.handlers
import asyncio
import json
from typing import Optional, Dict, Any, List
from datetime import datetime
import threading
import queue
import time


class SafeConsoleHandler(logging.StreamHandler):
    """Console handler with safe Unicode handling for Windows"""

    def __init__(self, stream=None):
        super().__init__(stream)
        self.encoding_errors = 'replace'  # Replace problematic characters

    def emit(self, record):
        """Emit a record with safe Unicode handling"""
        try:
            msg = self.format(record)
            stream = self.stream

            # Handle encoding issues on Windows
            if hasattr(stream, 'encoding') and stream.encoding:
                try:
                    # Try to encode the message
                    msg.encode(stream.encoding)
                except UnicodeEncodeError:
                    # Replace problematic characters
                    msg = msg.encode(stream.encoding, errors='replace').decode(stream.encoding)

            # Write the message
            stream.write(msg + self.terminator)
            self.flush()

        except Exception:
            self.handleError(record)


class RotatingFileHandler(logging.handlers.RotatingFileHandler):
    """Enhanced rotating file handler with better error handling"""

    def __init__(self, filename, mode='a', maxBytes=0, backupCount=0, encoding=None, delay=False):
        # Default to UTF-8 encoding for file handlers
        if encoding is None:
            encoding = 'utf-8'
        super().__init__(filename, mode, maxBytes, backupCount, encoding, delay)
        self.error_count = 0
        self.max_errors = 10

    def emit(self, record):
        """Emit a record with error handling"""
        try:
            super().emit(record)
            self.error_count = 0  # Reset error count on success
        except Exception as e:
            self.error_count += 1
            if self.error_count <= self.max_errors:
                self.handleError(record)
            # Stop trying after max errors to prevent infinite loops


class TimedRotatingFileHandler(logging.handlers.TimedRotatingFileHandler):
    """Enhanced timed rotating file handler with better error handling"""
    
    def __init__(self, filename, when='h', interval=1, backupCount=0, encoding=None, delay=False, utc=False):
        super().__init__(filename, when, interval, backupCount, encoding, delay, utc)
        self.error_count = 0
        self.max_errors = 10
        
    def emit(self, record):
        """Emit a record with error handling"""
        try:
            super().emit(record)
            self.error_count = 0  # Reset error count on success
        except Exception as e:
            self.error_count += 1
            if self.error_count <= self.max_errors:
                self.handleError(record)


class DatabaseHandler(logging.Handler):
    """Handler for logging to database"""
    
    def __init__(self, table_name='system_logs', max_buffer_size=100, flush_interval=30):
        super().__init__()
        self.table_name = table_name
        self.max_buffer_size = max_buffer_size
        self.flush_interval = flush_interval
        self.buffer: List[Dict[str, Any]] = []
        self.buffer_lock = threading.Lock()
        self.last_flush = time.time()
        self._setup_table_creation()
        
    def _setup_table_creation(self):
        """Setup table creation for logs"""
        self.create_table_sql = f"""
        CREATE TABLE IF NOT EXISTS {self.table_name} (
            id SERIAL PRIMARY KEY,
            timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            level VARCHAR(20) NOT NULL,
            logger VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            module VARCHAR(255),
            function VARCHAR(255),
            line_number INTEGER,
            user_id VARCHAR(255),
            database_name VARCHAR(255),
            request_id VARCHAR(255),
            extra_data JSONB,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_{self.table_name}_timestamp ON {self.table_name}(timestamp);
        CREATE INDEX IF NOT EXISTS idx_{self.table_name}_level ON {self.table_name}(level);
        CREATE INDEX IF NOT EXISTS idx_{self.table_name}_logger ON {self.table_name}(logger);
        """
        
    def emit(self, record):
        """Emit a record to database buffer"""
        try:
            log_entry = self._format_record(record)
            
            with self.buffer_lock:
                self.buffer.append(log_entry)
                
                # Flush if buffer is full or enough time has passed
                should_flush = (
                    len(self.buffer) >= self.max_buffer_size or
                    time.time() - self.last_flush >= self.flush_interval
                )
                
            if should_flush:
                self._flush_buffer()
                
        except Exception:
            self.handleError(record)
            
    def _format_record(self, record) -> Dict[str, Any]:
        """Format log record for database storage"""
        # Get formatted message
        message = self.format(record) if self.formatter else record.getMessage()
        
        # Extract extra data
        extra_data = {}
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info',
                          'exc_text', 'stack_info', 'user_id', 'database', 'request_id']:
                try:
                    # Only include JSON-serializable values
                    json.dumps(value)
                    extra_data[key] = value
                except (TypeError, ValueError):
                    extra_data[key] = str(value)
                    
        return {
            'timestamp': datetime.fromtimestamp(record.created),
            'level': record.levelname,
            'logger': record.name,
            'message': message,
            'module': record.module,
            'function': record.funcName,
            'line_number': record.lineno,
            'user_id': getattr(record, 'user_id', None),
            'database_name': getattr(record, 'database', None),
            'request_id': getattr(record, 'request_id', None),
            'extra_data': extra_data if extra_data else None
        }
        
    def _flush_buffer(self):
        """Flush buffer to database"""
        if not self.buffer:
            return
            
        try:
            # This would need to be implemented with actual database connection
            # For now, we'll just clear the buffer
            # In a real implementation, you'd use the ERP database connection
            
            with self.buffer_lock:
                # TODO: Implement actual database insertion
                # await self._insert_logs(self.buffer)
                self.buffer.clear()
                self.last_flush = time.time()
                
        except Exception as e:
            # Log error to console as fallback
            print(f"Failed to flush logs to database: {e}")
            
    def flush(self):
        """Flush any pending records"""
        self._flush_buffer()
        
    def close(self):
        """Close handler and flush remaining records"""
        self.flush()
        super().close()


class AsyncHandler(logging.Handler):
    """Asynchronous logging handler that doesn't block the main thread"""
    
    def __init__(self, target_handler, max_queue_size=1000):
        super().__init__()
        self.target_handler = target_handler
        self.max_queue_size = max_queue_size
        self.log_queue = queue.Queue(maxsize=max_queue_size)
        self.worker_thread = None
        self.shutdown_event = threading.Event()
        self._start_worker()
        
    def _start_worker(self):
        """Start the worker thread"""
        self.worker_thread = threading.Thread(target=self._worker, daemon=True)
        self.worker_thread.start()
        
    def _worker(self):
        """Worker thread that processes log records"""
        while not self.shutdown_event.is_set():
            try:
                # Wait for a record with timeout
                record = self.log_queue.get(timeout=1.0)
                if record is None:  # Shutdown signal
                    break
                    
                # Process the record
                self.target_handler.emit(record)
                self.log_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                # Handle errors in worker thread
                print(f"Error in async logging worker: {e}")
                
    def emit(self, record):
        """Emit a record asynchronously"""
        try:
            if not self.shutdown_event.is_set():
                self.log_queue.put_nowait(record)
        except queue.Full:
            # Queue is full, drop the record or handle as needed
            pass
        except Exception:
            self.handleError(record)
            
    def close(self):
        """Close the handler and shutdown worker thread"""
        # Signal shutdown
        self.shutdown_event.set()
        
        # Send shutdown signal to worker
        try:
            self.log_queue.put_nowait(None)
        except queue.Full:
            pass
            
        # Wait for worker to finish
        if self.worker_thread and self.worker_thread.is_alive():
            self.worker_thread.join(timeout=5.0)
            
        # Close target handler
        self.target_handler.close()
        super().close()


class MemoryHandler(logging.Handler):
    """In-memory handler for temporary log storage"""
    
    def __init__(self, max_records=1000):
        super().__init__()
        self.max_records = max_records
        self.records: List[logging.LogRecord] = []
        self.lock = threading.Lock()
        
    def emit(self, record):
        """Emit a record to memory"""
        with self.lock:
            self.records.append(record)
            
            # Keep only the most recent records
            if len(self.records) > self.max_records:
                self.records = self.records[-self.max_records:]
                
    def get_records(self, level=None, limit=None) -> List[logging.LogRecord]:
        """Get stored records"""
        with self.lock:
            records = self.records.copy()
            
        # Filter by level if specified
        if level is not None:
            level_num = getattr(logging, level.upper()) if isinstance(level, str) else level
            records = [r for r in records if r.levelno >= level_num]
            
        # Limit results if specified
        if limit is not None:
            records = records[-limit:]
            
        return records
        
    def clear(self):
        """Clear all stored records"""
        with self.lock:
            self.records.clear()


class WebSocketHandler(logging.Handler):
    """Handler for sending logs to WebSocket clients"""
    
    def __init__(self):
        super().__init__()
        self.clients = set()
        
    def add_client(self, websocket):
        """Add a WebSocket client"""
        self.clients.add(websocket)
        
    def remove_client(self, websocket):
        """Remove a WebSocket client"""
        self.clients.discard(websocket)
        
    def emit(self, record):
        """Emit a record to all connected WebSocket clients"""
        if not self.clients:
            return
            
        try:
            message = self.format(record)
            
            # Send to all clients (this would need actual WebSocket implementation)
            for client in self.clients.copy():
                try:
                    # TODO: Implement actual WebSocket sending
                    # await client.send(message)
                    pass
                except Exception:
                    # Remove disconnected clients
                    self.clients.discard(client)
                    
        except Exception:
            self.handleError(record)
