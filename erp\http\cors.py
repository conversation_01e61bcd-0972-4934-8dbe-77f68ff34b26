"""
CORS (Cross-Origin Resource Sharing) handling for HTTP routes
"""

import re
from typing import Optional
from fastapi import Request, Response
from fastapi.responses import JSONResponse

from ..logging import get_logger

logger = get_logger(__name__)


async def cors_handler(request: Request, cors_pattern: str) -> Optional[Response]:
    """
    Handle CORS for a specific route
    
    Args:
        request: FastAPI request object
        cors_pattern: CORS origin pattern (e.g., "*", "https://example.com", "*.example.com")
        
    Returns:
        Response if CORS preflight, None to continue processing
    """
    
    origin = request.headers.get('origin')
    
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        return _create_cors_response(origin, cors_pattern, is_preflight=True)
    
    # For actual requests, we'll add CORS headers in the response
    # This is handled by the route wrapper
    return None


def _create_cors_response(origin: Optional[str], cors_pattern: str, is_preflight: bool = False) -> Response:
    """
    Create CORS response with appropriate headers
    
    Args:
        origin: Request origin
        cors_pattern: CORS pattern to match against
        is_preflight: Whether this is a preflight request
        
    Returns:
        Response with CORS headers
    """
    
    headers = {}
    
    # Check if origin is allowed
    if _is_origin_allowed(origin, cors_pattern):
        headers['Access-Control-Allow-Origin'] = origin or '*'
        headers['Access-Control-Allow-Credentials'] = 'true'
    elif cors_pattern == '*':
        headers['Access-Control-Allow-Origin'] = '*'
    
    if is_preflight:
        # Preflight response headers
        headers.update({
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept, Origin',
            'Access-Control-Max-Age': '86400'  # 24 hours
        })
        
        return Response(status_code=200, headers=headers)
    else:
        # Regular response headers
        headers.update({
            'Access-Control-Expose-Headers': 'Content-Type, X-Process-Time'
        })
        
        return JSONResponse(content={}, headers=headers)


def _is_origin_allowed(origin: Optional[str], cors_pattern: str) -> bool:
    """
    Check if origin matches the CORS pattern
    
    Args:
        origin: Request origin
        cors_pattern: Pattern to match (supports wildcards)
        
    Returns:
        True if origin is allowed
    """
    
    if not origin or not cors_pattern:
        return False
    
    if cors_pattern == '*':
        return True
    
    if cors_pattern == origin:
        return True
    
    # Handle wildcard patterns
    if '*' in cors_pattern:
        # Convert wildcard pattern to regex
        regex_pattern = cors_pattern.replace('.', r'\.').replace('*', '.*')
        regex_pattern = f'^{regex_pattern}$'
        
        try:
            return bool(re.match(regex_pattern, origin))
        except re.error:
            logger.warning(f"Invalid CORS pattern: {cors_pattern}")
            return False
    
    return False


def add_cors_headers(response: Response, origin: Optional[str], cors_pattern: str) -> Response:
    """
    Add CORS headers to an existing response
    
    Args:
        response: Response object to modify
        origin: Request origin
        cors_pattern: CORS pattern
        
    Returns:
        Modified response with CORS headers
    """
    
    if _is_origin_allowed(origin, cors_pattern):
        response.headers['Access-Control-Allow-Origin'] = origin or '*'
        response.headers['Access-Control-Allow-Credentials'] = 'true'
    elif cors_pattern == '*':
        response.headers['Access-Control-Allow-Origin'] = '*'
    
    response.headers['Access-Control-Expose-Headers'] = 'Content-Type, X-Process-Time'
    
    return response


# TODO: Add additional CORS features when needed:
# - Custom allowed methods per route
# - Custom allowed headers per route  
# - CORS configuration from settings
# - Domain-specific CORS policies
# - CORS caching strategies
