"""
Logging Coordinator - Prevents duplicate logging and manages log levels
"""
import logging
import threading
from typing import Set, Dict, Any, Optional
from contextlib import contextmanager


class LoggingCoordinator:
    """
    Coordinates logging across the system to prevent duplicates and manage verbosity
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._lock = threading.Lock()
        self._active_operations: Set[str] = set()
        self._operation_contexts: Dict[str, Dict[str, Any]] = {}
        self._suppressed_loggers: Set[str] = set()
        
    @contextmanager
    def operation_context(self, operation_id: str, **context):
        """
        Context manager for tracking operations to prevent duplicate logging
        
        Args:
            operation_id: Unique identifier for the operation
            **context: Additional context information
        """
        with self._lock:
            if operation_id in self._active_operations:
                # Operation already active, suppress duplicate logging
                yield False
                return
                
            self._active_operations.add(operation_id)
            self._operation_contexts[operation_id] = context
            
        try:
            yield True
        finally:
            with self._lock:
                self._active_operations.discard(operation_id)
                self._operation_contexts.pop(operation_id, None)
    
    def is_operation_active(self, operation_id: str) -> bool:
        """Check if an operation is currently active"""
        with self._lock:
            return operation_id in self._active_operations
    
    def suppress_logger(self, logger_name: str):
        """Temporarily suppress a logger to reduce verbosity"""
        with self._lock:
            self._suppressed_loggers.add(logger_name)
            logger = logging.getLogger(logger_name)
            logger.setLevel(logging.WARNING)  # Only show warnings and errors
    
    def restore_logger(self, logger_name: str):
        """Restore a logger's normal logging level"""
        with self._lock:
            self._suppressed_loggers.discard(logger_name)
            logger = logging.getLogger(logger_name)
            logger.setLevel(logging.INFO)  # Restore to normal level
    
    @contextmanager
    def quiet_operation(self, *logger_names):
        """
        Context manager to temporarily reduce logging verbosity for specific loggers
        
        Args:
            *logger_names: Names of loggers to quiet during the operation
        """
        for logger_name in logger_names:
            self.suppress_logger(logger_name)
        
        try:
            yield
        finally:
            for logger_name in logger_names:
                self.restore_logger(logger_name)
    
    def get_active_operations(self) -> Dict[str, Dict[str, Any]]:
        """Get currently active operations and their contexts"""
        with self._lock:
            return self._operation_contexts.copy()


# Global coordinator instance
_coordinator = LoggingCoordinator()


def get_logging_coordinator() -> LoggingCoordinator:
    """Get the global logging coordinator instance"""
    return _coordinator


def operation_context(operation_id: str, **context):
    """Convenience function for operation context"""
    return _coordinator.operation_context(operation_id, **context)


def quiet_operation(*logger_names):
    """Convenience function for quiet operation"""
    return _coordinator.quiet_operation(*logger_names)


def is_operation_active(operation_id: str) -> bool:
    """Convenience function to check if operation is active"""
    return _coordinator.is_operation_active(operation_id)
