# ERP Development Guide

## Getting Started

### Prerequisites
- Python 3.12+
- PostgreSQL 16+
- <PERSON><PERSON> and <PERSON><PERSON> Compose (optional)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/totalard/erp_py.git
   cd erp_py
   ```

2. **Set up virtual environment**
   ```bash
   # Automated setup (recommended)
   python scripts/setup_env.py

   # Manual setup (if needed)
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Start PostgreSQL (Docker)**
   ```bash
   docker-compose up -d postgres pgadmin
   ```

4. **Configure the system**
   ```bash
   cp config/erp.conf.example-multi-db erp.conf
   # Edit erp.conf with your database settings
   ```

5. **Start the ERP server**
   ```bash
   python erp-bin start
   ```

## Addon Development

### Creating a New Addon

1. **Create addon directory structure**
   ```
   addons/my_addon/
   ├── __init__.py
   ├── __manifest__.py
   ├── models/
   │   ├── __init__.py
   │   └── my_model.py
   ├── controllers/
   │   ├── __init__.py
   │   └── my_controller.py
   └── hooks.py
   ```

2. **Define the manifest**
   ```python
   # __manifest__.py
   {
       'name': 'My Addon',
       'version': '1.0.0',
       'description': 'My custom addon',
       'depends': ['base'],
       'installable': True,
       'auto_install': False,
   }
   ```

3. **Create models**
   ```python
   # models/my_model.py
   from erp.models import Model
   from erp.fields import Char, Integer, Boolean

   class MyModel(Model):
       _name = 'my.model'
       _description = 'My Model'

       name = Char(string='Name', required=True)
       value = Integer(string='Value')
       active = Boolean(string='Active', default=True)
   ```

4. **Create controllers**
   ```python
   # controllers/my_controller.py
   from erp.http import route, Controller
   from erp.http.response import Response

   class MyController(Controller):
       
       @route('/my_addon/hello', methods=['GET'])
       async def hello(self, request):
           return self.json_response({'message': 'Hello from my addon!'})
   ```

### Model Development

#### Field Types
```python
from erp.fields import (
    Char, Text, Integer, Float, Boolean,
    Date, DateTime, Selection, Many2one, One2many
)

class ExampleModel(Model):
    _name = 'example.model'
    
    # Basic fields
    name = Char(string='Name', size=100, required=True)
    description = Text(string='Description')
    count = Integer(string='Count', default=0)
    price = Float(string='Price', digits=(10, 2))
    active = Boolean(string='Active', default=True)
    
    # Date fields
    date_created = Date(string='Created Date')
    datetime_modified = DateTime(string='Modified DateTime')
    
    # Selection field
    state = Selection([
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('done', 'Done')
    ], string='State', default='draft')
```

#### Model Methods
```python
class ExampleModel(Model):
    _name = 'example.model'
    
    async def create(self, values):
        """Override create method"""
        # Custom logic before creation
        result = await super().create(values)
        # Custom logic after creation
        return result
    
    async def write(self, values):
        """Override write method"""
        # Custom logic before update
        result = await super().write(values)
        # Custom logic after update
        return result
    
    async def unlink(self):
        """Override unlink method"""
        # Custom logic before deletion
        result = await super().unlink()
        return result
```

### Controller Development

#### Route Decorators
```python
from erp.http import route, Controller

class MyController(Controller):
    
    @route('/api/data', methods=['GET'], type='json')
    async def get_data(self, request):
        """JSON API endpoint"""
        return self.json_response({'data': 'example'})
    
    @route('/page', methods=['GET'], type='http')
    async def show_page(self, request):
        """HTML page endpoint"""
        context = {'title': 'My Page'}
        return self.render_template('my_template.xml', context)
    
    @route('/redirect', methods=['GET'])
    async def redirect_example(self, request):
        """Redirect example"""
        return self.redirect('/other_page')
```

#### Database Access in Controllers
```python
class MyController(Controller):
    
    @route('/api/records', methods=['GET'])
    async def get_records(self, request):
        """Access database records"""
        env = request.state.env
        
        # Get model
        MyModel = env['my.model']
        
        # Search records
        records = await MyModel.search([('active', '=', True)])
        
        # Return data
        data = []
        for record in records:
            data.append({
                'id': record.id,
                'name': record.name,
                'value': record.value
            })
        
        return self.json_response({'records': data})
```

### Lifecycle Hooks

#### Installation Hooks
```python
# hooks.py
from erp.addons.hooks import pre_install_hook, post_install_hook

@pre_install_hook('my_addon', priority=50)
async def pre_install(context):
    """Pre-installation hook"""
    logger = context.logger
    logger.info("Running pre-install hook for my_addon")
    
    # Custom pre-installation logic
    # Return True for success, False for failure
    return True

@post_install_hook('my_addon', priority=50)
async def post_install(context):
    """Post-installation hook"""
    env = context.env
    logger = context.logger
    
    logger.info("Running post-install hook for my_addon")
    
    # Create default data
    MyModel = env['my.model']
    await MyModel.create({
        'name': 'Default Record',
        'value': 100
    })
    
    return True
```

### Template Development

#### XML Templates
```xml
<!-- templates/my_template.xml -->
<templates>
    <t t-name="my_addon.page">
        <html>
            <head>
                <title t-esc="title"/>
            </head>
            <body>
                <h1 t-esc="title"/>
                <div t-if="records">
                    <t t-foreach="records" t-as="record">
                        <p t-esc="record.name"/>
                    </t>
                </div>
            </body>
        </html>
    </t>
</templates>
```

#### Template Usage in Controllers
```python
@route('/my_page', methods=['GET'])
async def my_page(self, request):
    """Render template with data"""
    env = request.state.env
    
    # Get data
    MyModel = env['my.model']
    records = await MyModel.search([])
    
    # Prepare context
    context = {
        'title': 'My Page',
        'records': records
    }
    
    return self.render_template('my_addon.page', context)
```

## Testing

Testing functionality has been removed due to architectural changes. Tests will be reimplemented in future versions with the new architecture.

## Debugging

### Logging
```python
from erp.logging import get_logger

logger = get_logger(__name__)

# Log levels
logger.debug("Debug message")
logger.info("Info message")
logger.warning("Warning message")
logger.error("Error message")
```

### SQL Debugging
Set log level to DEBUG in erp.conf to see SQL queries:
```ini
[options]
log_level = DEBUG
```

### Environment Inspection
```python
# In controller or model method
env = request.state.env  # or self.env in models

# Check current database
print(f"Database: {env.cr.database}")

# Check current user
print(f"User ID: {env.uid}")

# Check context
print(f"Context: {env.context}")
```

## Best Practices

1. **Always use async/await** for database operations
2. **Follow naming conventions** (_name, _description for models)
3. **Use proper field types** with appropriate constraints
4. **Implement proper error handling** in hooks and controllers
5. **Write comprehensive tests** for all functionality
6. **Use logging** for debugging and monitoring
7. **Follow dependency order** in addon manifests
8. **Keep controllers thin** - business logic in models
9. **Use transactions** for data consistency
10. **Document your code** with docstrings and comments
