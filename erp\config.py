"""
Configuration management for ERP system
"""
import os
import configparser
from typing import Dict, Any, Optional, List


class Config:
    """Configuration manager"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config = configparser.ConfigParser()
        self.config_file = config_file or self._find_config_file()
        self._load_config()
    
    def _find_config_file(self) -> str:
        """Find configuration file"""
        possible_paths = [
            'erp.conf',  # Root directory first (new priority)
            'config/erp.conf',
            '/etc/erp/erp.conf',
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # Return default path if none found
        return 'erp.conf'
    
    def _load_config(self):
        """Load configuration from file"""
        if os.path.exists(self.config_file):
            self.config.read(self.config_file)
        else:
            # Set default values
            self._set_defaults()
    
    def _set_defaults(self):
        """Set default configuration values"""
        self.config.add_section('options')
        self.config.set('options', 'http_port', '8069')
        self.config.set('options', 'http_interface', '127.0.0.1')
        self.config.set('options', 'db_host', 'localhost')
        self.config.set('options', 'db_port', '5432')
        self.config.set('options', 'db_user', 'erp')
        self.config.set('options', 'db_password', 'erp')
        self.config.set('options', 'db_name', 'erp_db')
        self.config.set('options', 'list_db', 'True')
        self.config.set('options', 'db_filter', '.*')
        self.config.set('options', 'addons_path', 'addons')
        self.config.set('options', 'log_level', 'info')
        self.config.set('options', 'log_file', 'logs/erp.log')
        self.config.set('options', 'log_console_enabled', 'true')
        self.config.set('options', 'log_console_level', 'info')
        self.config.set('options', 'log_colored_console', 'true')
        self.config.set('options', 'log_file_enabled', 'true')
        self.config.set('options', 'log_file_level', 'debug')
        self.config.set('options', 'log_file_rotating', 'true')
        self.config.set('options', 'log_file_max_bytes', '10485760')
        self.config.set('options', 'log_file_backup_count', '5')
        self.config.set('options', 'log_handlers', 'console,file')
        self.config.set('options', 'admin_passwd', 'admin')
        self.config.set('options', 'db_pool_min_size', '10')
        self.config.set('options', 'db_pool_max_size', '20')
        self.config.set('options', 'server_type', 'asgi')
        self.config.set('options', 'cors_origins', '*')
        self.config.set('options', 'cluster_mode', 'false')
        self.config.set('options', 'cluster_nodes', 'auto')
    
    def get(self, section: str, option: str, fallback: Any = None) -> Any:
        """Get configuration value"""
        try:
            return self.config.get(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError):
            return fallback
    
    def getint(self, section: str, option: str, fallback: int = 0) -> int:
        """Get integer configuration value"""
        try:
            return self.config.getint(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return fallback

    def getfloat(self, section: str, option: str, fallback: float = 0.0) -> float:
        """Get float configuration value"""
        try:
            return self.config.getfloat(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return fallback
    
    def getboolean(self, section: str, option: str, fallback: bool = False) -> bool:
        """Get boolean configuration value"""
        try:
            return self.config.getboolean(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return fallback
    
    def set(self, section: str, option: str, value: str):
        """Set configuration value"""
        if not self.config.has_section(section):
            self.config.add_section(section)
        self.config.set(section, option, value)
    
    def save(self):
        """Save configuration to file"""
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        with open(self.config_file, 'w') as f:
            self.config.write(f)

    def sections(self):
        """Get all configuration sections"""
        return self.config.sections()

    def options(self, section: str):
        """Get all options in a section"""
        try:
            return self.config.options(section)
        except configparser.NoSectionError:
            return []

    def has_section(self, section: str) -> bool:
        """Check if section exists"""
        return self.config.has_section(section)
    
    @property
    def db_config(self) -> Dict[str, Any]:
        """Get database configuration"""
        return {
            'host': self.get('options', 'db_host', 'localhost'),
            'port': self.getint('options', 'db_port', 5432),
            'user': self.get('options', 'db_user', 'erp'),
            'password': self.get('options', 'db_password', 'erp'),
            'database': self.get('options', 'db_name', 'erp_db'),
        }
    
    @property
    def server_config(self) -> Dict[str, Any]:
        """Get server configuration"""
        return {
            'host': self.get('options', 'http_interface', '127.0.0.1'),
            'port': self.getint('options', 'http_port', 8069),
        }
    

    
    @property
    def addons_paths(self) -> List[str]:
        """Get all addons paths as a list"""
        paths_str = self.get('options', 'addons_path', 'addons')
        # Split by comma and strip whitespace
        paths = [path.strip() for path in paths_str.split(',') if path.strip()]
        return paths if paths else ['addons']

    @property
    def addons_path(self) -> str:
        """Get the first addons path (for backward compatibility)"""
        paths = self.addons_paths
        return paths[0] if paths else 'addons'

    @property
    def logging_config(self) -> Dict[str, Any]:
        """Get logging configuration"""
        return {
            # Basic settings
            'log_level': self.get('options', 'log_level', 'info'),
            'log_file': self.get('options', 'log_file', 'logs/erp.log'),

            # Console logging
            'log_console_enabled': self.getboolean('options', 'log_console_enabled', True),
            'log_console_level': self.get('options', 'log_console_level', 'info'),
            'log_colored_console': self.getboolean('options', 'log_colored_console', True),

            # File logging
            'log_file_enabled': self.getboolean('options', 'log_file_enabled', True),
            'log_file_level': self.get('options', 'log_file_level', 'debug'),
            'log_file_rotating': self.getboolean('options', 'log_file_rotating', True),
            'log_file_max_bytes': self.getint('options', 'log_file_max_bytes', 10485760),
            'log_file_backup_count': self.getint('options', 'log_file_backup_count', 5),
            'log_file_json': self.getboolean('options', 'log_file_json', False),

            # Timed rotating
            'log_file_timed_rotating': self.getboolean('options', 'log_file_timed_rotating', False),
            'log_file_when': self.get('options', 'log_file_when', 'midnight'),
            'log_file_interval': self.getint('options', 'log_file_interval', 1),

            # JSON logging
            'log_json_enabled': self.getboolean('options', 'log_json_enabled', False),
            'log_json_include_extra': self.getboolean('options', 'log_json_include_extra', True),

            # Database logging
            'log_database_enabled': self.getboolean('options', 'log_database_enabled', False),
            'log_database_level': self.get('options', 'log_database_level', 'warning'),
            'log_database_table': self.get('options', 'log_database_table', 'system_logs'),
            'log_database_buffer_size': self.getint('options', 'log_database_buffer_size', 100),

            # Performance logging
            'log_performance_enabled': self.getboolean('options', 'log_performance_enabled', True),
            'log_performance_threshold': self.getfloat('options', 'log_performance_threshold', 1.0),

            # Security logging
            'log_security_enabled': self.getboolean('options', 'log_security_enabled', True),
            'log_security_level': self.get('options', 'log_security_level', 'warning'),

            # Rate limiting
            'log_rate_limit_enabled': self.getboolean('options', 'log_rate_limit_enabled', False),
            'log_rate_limit_max_records': self.getint('options', 'log_rate_limit_max_records', 100),
            'log_rate_limit_time_window': self.getfloat('options', 'log_rate_limit_time_window', 60.0),

            # Duplicate filtering
            'log_duplicate_filter_enabled': self.getboolean('options', 'log_duplicate_filter_enabled', False),
            'log_duplicate_max_count': self.getint('options', 'log_duplicate_max_count', 5),
            'log_duplicate_time_window': self.getfloat('options', 'log_duplicate_time_window', 60.0),

            # Module filtering
            'log_module_filter_enabled': self.getboolean('options', 'log_module_filter_enabled', False),
            'log_module_filter_include': self._parse_list('options', 'log_module_filter_include'),
            'log_module_filter_exclude': self._parse_list('options', 'log_module_filter_exclude'),

            # Handlers
            'log_handlers': self._parse_list('options', 'log_handlers', ['console', 'file']),
        }

    def _parse_list(self, section: str, option: str, default: Optional[List[str]] = None) -> List[str]:
        """Parse comma-separated list from config"""
        value = self.get(section, option, '')
        if not value:
            return default or []
        return [item.strip() for item in value.split(',') if item.strip()]
    
    @property
    def list_db(self) -> bool:
        """Check if database listing is enabled"""
        return self.getboolean('options', 'list_db', True)

    @property
    def db_pool_config(self) -> Dict[str, Any]:
        """Get database pool configuration"""
        return {
            'min_size': self.getint('options', 'db_pool_min_size', 10),
            'max_size': self.getint('options', 'db_pool_max_size', 20),
        }

    @property
    def is_single_db_mode(self) -> bool:
        """Check if running in single database mode"""
        # If db_name is explicitly set and not empty, use single database mode
        db_name = self.get('options', 'db_name', '').strip()
        return bool(db_name)

    @property
    def is_multi_db_mode(self) -> bool:
        """Check if running in multi-database mode"""
        return not self.is_single_db_mode

    @property
    def db_filter(self) -> Optional[str]:
        """Get database filter pattern for multi-database mode
        
        Returns:
            str: Filter pattern if set
            None: If filter is null/empty (load all databases)
        """
        filter_value = self.get('options', 'db_filter', '').strip()
        return filter_value if filter_value else None

    @property
    def cors_origins(self) -> list:
        """Get CORS origins configuration"""
        origins_str = self.get('options', 'cors_origins', '*')
        if origins_str == '*':
            return ['*']
        return [origin.strip() for origin in origins_str.split(',') if origin.strip()]

    def get_default_database(self) -> Optional[str]:
        """Get default database name based on mode"""
        if self.is_single_db_mode:
            return self.get('options', 'db_name')
        return None

    @property
    def cluster_mode(self) -> bool:
        """Check if cluster mode is enabled"""
        return self.getboolean('options', 'cluster_mode', False)

    @property
    def cluster_nodes(self) -> str:
        """Get cluster nodes configuration"""
        return self.get('options', 'cluster_nodes', 'auto')

    @property
    def is_development_mode(self) -> bool:
        """Check if running in development mode"""
        # Check environment variable first
        import os
        env_dev = os.getenv('ERP_DEV_MODE', '').lower() in ('true', '1', 'yes', 'on')
        if env_dev:
            return True
        
        # Check config file
        return self.getboolean('options', 'dev_mode', False)


# Global configuration instance
config = Config()
