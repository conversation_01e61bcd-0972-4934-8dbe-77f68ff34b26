"""
API routes for database management operations
"""
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional
import re

from ..database.registry import DatabaseRegistry
from ..database.registry.lifecycle import DatabaseLifecycle
from ..utils.responses import APIResponse

router = APIRouter(prefix="/api", tags=["api"])


class CreateDatabaseRequest(BaseModel):
    """Request model for database creation"""
    name: str
    description: Optional[str] = None


def validate_database_name(name: str) -> bool:
    """Validate database name according to PostgreSQL rules"""
    if not name:
        return False
    
    # PostgreSQL database name rules:
    # - Must start with a letter or underscore
    # - Can contain letters, digits, underscores, and dollar signs
    # - Maximum length is 63 characters
    # - Case insensitive (converted to lowercase)
    
    if len(name) > 63:
        return False
    
    # Check if starts with letter or underscore
    if not re.match(r'^[a-zA-Z_]', name):
        return False
    
    # Check if contains only valid characters
    if not re.match(r'^[a-zA-Z0-9_$]+$', name):
        return False
    
    # Reserved names to avoid
    reserved_names = {
        'postgres', 'template0', 'template1', 'information_schema', 
        'pg_catalog', 'pg_toast', 'pg_temp', 'pg_toast_temp'
    }
    
    if name.lower() in reserved_names:
        return False
    
    return True


@router.post("/databases")
async def create_database(request: CreateDatabaseRequest):
    """Create a new database"""
    try:
        # Validate database name
        if not validate_database_name(request.name):
            return APIResponse.error(
                "Invalid database name. Must start with letter/underscore, "
                "contain only letters, digits, underscores, and dollar signs, "
                "and be max 63 characters long.",
                400
            )
        
        # Convert to lowercase (PostgreSQL convention)
        db_name = request.name.lower()
        
        # Create the database
        success = await DatabaseLifecycle.create_database(db_name)
        
        if success:
            return APIResponse.success({
                "message": f"Database '{db_name}' created successfully",
                "database_name": db_name
            })
        else:
            return APIResponse.error(
                f"Failed to create database '{db_name}'. It may already exist.",
                409
            )
            
    except Exception as e:
        return APIResponse.error(
            f"Error creating database: {str(e)}",
            500
        )


@router.get("/databases")
async def list_databases():
    """List all available databases"""
    try:
        databases = await DatabaseRegistry.list_databases()
        return APIResponse.success({
            "databases": databases,
            "count": len(databases)
        })
    except Exception as e:
        return APIResponse.error(
            f"Error listing databases: {str(e)}",
            status_code=500
        )


@router.delete("/databases/{db_name}")
async def delete_database(db_name: str):
    """Delete a database"""
    try:
        # Validate database name
        if not validate_database_name(db_name):
            return APIResponse.error(
                "Invalid database name",
                400
            )
        
        # Convert to lowercase
        db_name = db_name.lower()
        
        # Check if it's a system database
        system_dbs = {'postgres', 'template0', 'template1'}
        if db_name in system_dbs:
            return APIResponse.error(
                f"Cannot delete system database '{db_name}'",
                403
            )
        
        # Delete the database
        success = await DatabaseRegistry.drop_database(db_name)
        
        if success:
            return APIResponse.success({
                "message": f"Database '{db_name}' deleted successfully"
            })
        else:
            return APIResponse.error(
                f"Failed to delete database '{db_name}'",
                status_code=500
            )
            
    except Exception as e:
        return APIResponse.error(
            f"Error deleting database: {str(e)}",
            status_code=500
        )