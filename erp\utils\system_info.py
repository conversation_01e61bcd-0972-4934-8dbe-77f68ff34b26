"""
System Information Utilities
Provides system information for startup and monitoring
"""
import os
import sys
import platform
import psutil
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
from ..config import config
from ..logging import get_logger

logger = get_logger(__name__)


class SystemInfo:
    """System information collector"""
    
    @staticmethod
    def get_python_info() -> Dict[str, Any]:
        """Get Python runtime information"""
        return {
            'version': sys.version.split()[0],
            'implementation': platform.python_implementation(),
            'executable': sys.executable,
            'platform': platform.platform(),
            'architecture': platform.architecture()[0]
        }
    
    @staticmethod
    def get_system_resources() -> Dict[str, Any]:
        """Get system resource information"""
        try:
            memory = psutil.virtual_memory()
            cpu_count = psutil.cpu_count()
            
            return {
                'cpu_cores': cpu_count,
                'memory_total_gb': round(memory.total / (1024**3), 2),
                'memory_available_gb': round(memory.available / (1024**3), 2),
                'memory_percent': memory.percent,
                'platform': platform.system(),
                'hostname': platform.node()
            }
        except Exception as e:
            logger.debug(f"Error getting system resources: {e}")
            return {'error': str(e)}
    
    @staticmethod
    def get_process_info() -> Dict[str, Any]:
        """Get current process information"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                'pid': process.pid,
                'memory_rss_mb': round(memory_info.rss / (1024**2), 2),
                'memory_vms_mb': round(memory_info.vms / (1024**2), 2),
                'cpu_percent': process.cpu_percent(),
                'threads': process.num_threads(),
                'started_at': datetime.fromtimestamp(process.create_time()).strftime('%Y-%m-%d %H:%M:%S')
            }
        except Exception as e:
            logger.debug(f"Error getting process info: {e}")
            return {'error': str(e)}
    
    @staticmethod
    def get_config_info() -> Dict[str, Any]:
        """Get configuration information"""
        return {
            'http_interface': config.get('options', 'http_interface', '127.0.0.1'),
            'http_port': config.getint('options', 'http_port', 8069),
            'db_host': config.get('options', 'db_host', 'localhost'),
            'db_port': config.getint('options', 'db_port', 5432),
            'db_user': config.get('options', 'db_user', 'erp'),
            'addons_path': config.get('options', 'addons_path', 'addons'),
            'log_level': config.get('options', 'log_level', 'info'),
            'list_db': config.getboolean('options', 'list_db', True)
        }
    
    @staticmethod
    def get_version_info() -> Dict[str, Any]:
        """Get version information"""
        return {
            'erp_version': '1.0.0',
            'build_date': '2025-01-19',
            'environment': 'development'  # Could be read from config
        }
    
    @classmethod
    def get_startup_info(cls) -> Dict[str, Any]:
        """Get comprehensive startup information"""
        return {
            'timestamp': datetime.now().isoformat(),
            'version': cls.get_version_info(),
            'python': cls.get_python_info(),
            'system': cls.get_system_resources(),
            'process': cls.get_process_info(),
            'config': cls.get_config_info()
        }
    
    @classmethod
    def print_startup_info(cls, db_name: Optional[str] = None):
        """Print clean startup information"""
        info = cls.get_startup_info()
        
        print(f"🚀 ERP System v{info['version']['erp_version']} starting...")
        print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # System information
        system = info['system']
        if 'error' not in system:
            print(f"💻 System: {system['platform']} on {system['hostname']}")
            print(f"🔧 CPU: {system['cpu_cores']} cores")
            print(f"💾 Memory: {system['memory_available_gb']:.1f}GB available of {system['memory_total_gb']:.1f}GB total")
        
        # Python information
        python = info['python']
        print(f"🐍 Python: {python['version']} ({python['implementation']}) on {python['architecture']}")
        
        # Configuration
        config_info = info['config']
        print(f"🌐 Server: http://{config_info['http_interface']}:{config_info['http_port']}")
        print(f"🗄️  Database: {config_info['db_user']}@{config_info['db_host']}:{config_info['db_port']}")
        
        if db_name:
            print(f"📊 Database: {db_name}")
        
        print(f"📁 Addons: {config_info['addons_path']}")
        print(f"📝 Log Level: {config_info['log_level'].upper()}")
        print()


def print_startup_banner(db_name: Optional[str] = None):
    """Print clean startup banner with system information"""
    SystemInfo.print_startup_info(db_name)


async def get_database_info(db_name: str) -> Dict[str, Any]:
    """Get database-specific information"""
    try:
        from ..database.registry import DatabaseRegistry
        from ..database.memory.registry_manager import MemoryRegistryManager
        
        # Get basic database info
        db_manager = await DatabaseRegistry.get_database(db_name)
        
        info = {
            'name': db_name,
            'connected': db_manager is not None,
            'pool_info': {}
        }
        
        # Get pool information if available
        if db_manager and hasattr(db_manager, 'pool') and db_manager.pool:
            try:
                pool = db_manager.pool
                info['pool_info'] = {
                    'size': pool.get_size() if hasattr(pool, 'get_size') else 'unknown',
                    'max_size': pool.get_max_size() if hasattr(pool, 'get_max_size') else 'unknown',
                    'min_size': pool.get_min_size() if hasattr(pool, 'get_min_size') else 'unknown'
                }
            except Exception as e:
                info['pool_info'] = {'error': str(e)}
        
        # Get registry information if available
        try:
            registry = await MemoryRegistryManager.get_registry(db_name)
            if registry:
                stats = registry.get_stats()
                installed_modules = await registry.get_installed_modules()
                routes = await registry.get_routes()
                
                info['registry'] = {
                    'loaded': True,
                    'modules_count': len(installed_modules),
                    'routes_count': len(routes),
                    'models_count': stats.get('models_count', 0),
                    'uptime_seconds': registry.get_uptime() if hasattr(registry, 'get_uptime') else 0
                }
            else:
                info['registry'] = {'loaded': False}
        except Exception as e:
            info['registry'] = {'error': str(e)}
        
        return info
        
    except Exception as e:
        logger.debug(f"Error getting database info: {e}")
        return {'name': db_name, 'error': str(e)}


def print_database_info(db_info: Dict[str, Any]):
    """Print database information"""
    if 'error' in db_info:
        print(f"❌ Database error: {db_info['error']}")
        return
    
    print(f"✅ Database '{db_info['name']}' connected")
    
    # Pool information
    pool_info = db_info.get('pool_info', {})
    if pool_info and 'error' not in pool_info:
        print(f"🔗 Connection pool: {pool_info.get('size', 'unknown')}/{pool_info.get('max_size', 'unknown')} connections")
    
    # Registry information
    registry_info = db_info.get('registry', {})
    if registry_info.get('loaded'):
        print(f"📚 Registry: {registry_info.get('modules_count', 0)} modules, {registry_info.get('models_count', 0)} models, {registry_info.get('routes_count', 0)} routes")
    elif 'error' not in registry_info:
        print("📚 Registry: Not loaded")
