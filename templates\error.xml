<?xml version="1.0" encoding="utf-8"?>
<templates id="template_error" xml:space="preserve">

    <!-- Error Page Template -->
    <t t-name="error.html">
        <html>
            <head>
                <title t-esc="title"/>
                <meta charset="utf-8"/>
                <meta name="viewport" content="width=device-width, initial-scale=1"/>
                <style>
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        margin: 0;
                        padding: 20px;
                        background-color: #f5f5f5;
                    }
                    .container {
                        max-width: 800px;
                        margin: 0 auto;
                        background: white;
                        padding: 30px;
                        border-radius: 8px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    }
                    .error-header {
                        text-align: center;
                        margin-bottom: 30px;
                        padding-bottom: 20px;
                        border-bottom: 2px solid #ffebee;
                    }
                    .error-header h1 {
                        color: #d32f2f;
                        margin: 0;
                        font-size: 2.5em;
                    }
                    .error-content {
                        background: #ffebee;
                        border: 1px solid #ffcdd2;
                        border-radius: 4px;
                        padding: 20px;
                        margin: 20px 0;
                    }
                    .error-message {
                        color: #d32f2f;
                        font-weight: 500;
                        margin-bottom: 10px;
                    }
                    .error-details {
                        color: #666;
                        font-family: monospace;
                        background: #f5f5f5;
                        padding: 10px;
                        border-radius: 4px;
                        margin-top: 10px;
                        word-break: break-all;
                    }
                    .actions {
                        text-align: center;
                        margin-top: 30px;
                    }
                    .btn {
                        display: inline-block;
                        padding: 10px 20px;
                        margin: 0 10px;
                        text-decoration: none;
                        border-radius: 4px;
                        font-weight: 500;
                        transition: background-color 0.3s;
                    }
                    .btn-primary {
                        background-color: #1976d2;
                        color: white;
                    }
                    .btn-primary:hover {
                        background-color: #1565c0;
                    }
                    .btn-secondary {
                        background-color: #757575;
                        color: white;
                    }
                    .btn-secondary:hover {
                        background-color: #616161;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="error-header">
                        <h1 t-esc="title"/>
                    </div>

                    <div class="error-content">
                        <div class="error-message">
                            An error occurred while processing your request.
                        </div>
                        <t t-if="error_message">
                            <div class="error-details" t-esc="error_message"/>
                        </t>
                    </div>

                    <div class="actions">
                        <a href="/app" class="btn btn-primary">Go to App</a>
                        <a href="/app/databases" class="btn btn-secondary">Database List</a>
                        <a href="javascript:history.back()" class="btn btn-secondary">Go Back</a>
                    </div>
                </div>
            </body>
        </html>
    </t>

</templates>
